/**
 * Loader Styles
 *
 * This file contains styles for various loaders and loading indicators
 * used throughout the application.
 */

/* Full-screen loader overlay */
.loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loader-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Loader card */
.loader-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 2rem;
  width: 90%;
  max-width: 400px;
  text-align: center;
  animation: fadeInUp 0.5s ease;
}

/* Spinner loader */
.spinner-loader {
  display: inline-block;
  width: 60px;
  height: 60px;
  margin-bottom: 1.5rem;
}

.spinner-loader:after {
  content: " ";
  display: block;
  width: 48px;
  height: 48px;
  margin: 8px;
  border-radius: 50%;
  border: 6px solid #3498db;
  border-color: #3498db transparent #3498db transparent;
  animation: spinner-loader 1.2s linear infinite;
}

@keyframes spinner-loader {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Pulse loader */
.pulse-loader {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: 1.5rem;
}

.pulse-loader div {
  position: absolute;
  border: 4px solid #3498db;
  opacity: 1;
  border-radius: 50%;
  animation: pulse-loader 1s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

.pulse-loader div:nth-child(2) {
  animation-delay: -0.5s;
}

@keyframes pulse-loader {
  0% {
    top: 36px;
    left: 36px;
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    top: 0px;
    left: 0px;
    width: 72px;
    height: 72px;
    opacity: 0;
  }
}

/* Dots loader */
.dots-loader {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 30px;
  margin-top: 1rem;
}

.dots-loader div {
  position: absolute;
  top: 10px;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: #6c757d;
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.dots-loader div:nth-child(1) {
  left: 8px;
  animation: dots-loader1 0.6s infinite;
}

.dots-loader div:nth-child(2) {
  left: 8px;
  animation: dots-loader2 0.6s infinite;
}

.dots-loader div:nth-child(3) {
  left: 32px;
  animation: dots-loader2 0.6s infinite;
}

.dots-loader div:nth-child(4) {
  left: 56px;
  animation: dots-loader3 0.6s infinite;
}

@keyframes dots-loader1 {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes dots-loader3 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}

@keyframes dots-loader2 {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(24px, 0);
  }
}

/* Fade in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Inline loader */
.inline-loader {
  display: inline-block;
  width: 20px;
  height: 20px;
  vertical-align: middle;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Button loader */
.btn-loader {
  position: relative;
}

.btn-loader .inline-loader {
  position: absolute;
  top: calc(50% - 10px);
  left: calc(50% - 10px);
}

.btn-loader.loading {
  color: transparent !important;
}

.btn-loader.loading:hover {
  color: transparent !important;
}

/* Loader text */
.loader-text {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #343a40;
}

.loader-subtext {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 0;
}

/* Loader with status */
.loader-status {
  width: 100%;
  margin-top: 1rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.loader-status-text {
  font-size: 0.9rem;
  color: #6c757d;
  text-align: center;
  font-style: italic;
}
