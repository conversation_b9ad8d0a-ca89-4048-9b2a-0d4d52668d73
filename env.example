# =============================================
# Application Core Configuration
# =============================================
DEBUG=False
LOG_LEVEL=INFO
APP_ENV=production

# =============================================
# Directory Configuration
# =============================================
TEMP_DIR=temp
OUTPUT_DIR=output
LOG_DIR=logs

# =============================================
# File Processing Configuration
# =============================================
MAX_FILE_SIZE_MB=20  # Maximum file size supported by HandwritingOCR API
SUPPORTED_FORMATS=.txt,.docx,.pdf,.jpg,.jpeg,.png,.tiff,.bmp,.gif
MAX_RETRIES=3
TIMEOUT_SECONDS=300  # 5 minutes timeout for OCR processing

# =============================================
# Web Interface Configuration
# =============================================
HOST=127.0.0.1
PORT=8501
ENABLE_CORS=False
SECRET_KEY=<replace_with_secure_random_key>
ALLOWED_ORIGINS=http://localhost:8501,http://127.0.0.1:8501

# =============================================
# API Configuration
# =============================================
# Handwriting OCR API
HANDWRITING_OCR_API_KEY=your_api_key_here
HANDWRITING_OCR_API_URL=https://www.handwritingocr.com/api/v3
HANDWRITING_OCR_DELETE_AFTER=86400  # Optional, defaults to 24 hours

# =============================================
# Processing Configuration
# =============================================
# Similarity and Confidence Thresholds
SIMILARITY_THRESHOLD=0.8
OCR_CONFIDENCE_THRESHOLD=0.7
MAX_BATCH_SIZE=10

# Performance Settings
WORKERS=4
CHUNK_SIZE=1000

# Cache Configuration
ENABLE_CACHE=True
CACHE_TTL=3600 