{% extends "base.html" %} {% block title %}Answer Mapping{% endblock %} {% block
header %}Answer Mapping{% endblock %} {% block header_actions %}
<div class="header-actions">
  <button class="btn btn-primary" onclick="saveMapping()">
    <i class="fas fa-save"></i> Save Mapping
  </button>
  <button class="btn btn-secondary" onclick="resetMapping()">
    <i class="fas fa-undo"></i> Reset
  </button>
</div>
{% endblock %} {% block content %}
<div class="mapping-container">
  <!-- File Upload Section -->
  <section class="upload-section glass-card">
    <div class="section-header">
      <h3><i class="fas fa-file-upload"></i> Upload Files</h3>
    </div>
    <div class="upload-content">
      <div class="file-upload-area" id="dragDropArea">
        <div class="upload-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <div class="upload-text">
          <h4>Drag & Drop Files Here</h4>
          <p>or</p>
          <label class="btn btn-outline-primary">
            <input type="file" id="fileInput" multiple hidden />
            Browse Files
          </label>
        </div>
        <div class="upload-progress" id="uploadProgress" style="display: none">
          <div class="progress-bar">
            <div class="progress-fill"></div>
          </div>
          <p class="progress-text">
            Uploading... <span id="progressPercent">0%</span>
          </p>
        </div>
      </div>
      <div class="uploaded-files" id="uploadedFiles">
        <!-- Uploaded files will be listed here -->
      </div>
    </div>
  </section>

  <!-- Mapping Configuration -->
  <section class="mapping-config glass-card">
    <div class="section-header">
      <h3><i class="fas fa-cog"></i> Mapping Configuration</h3>
    </div>
    <div class="config-content">
      <div class="form-grid">
        <div class="form-group">
          <label for="answerFormat">Answer Format</label>
          <select id="answerFormat" class="form-control">
            <option value="multiple_choice">Multiple Choice</option>
            <option value="written">Written Answer</option>
            <option value="mixed">Mixed Format</option>
          </select>
        </div>
        <div class="form-group">
          <label for="totalQuestions">Total Questions</label>
          <input
            type="number"
            id="totalQuestions"
            class="form-control"
            min="1"
          />
        </div>
        <div class="form-group">
          <label for="passingScore">Passing Score (%)</label>
          <input
            type="number"
            id="passingScore"
            class="form-control"
            min="0"
            max="100"
            value="60"
          />
        </div>
      </div>
    </div>
  </section>

  <!-- Answer Mapping -->
  <section class="answer-mapping glass-card">
    <div class="section-header">
      <h3><i class="fas fa-map-signs"></i> Answer Mapping</h3>
      <div class="mapping-actions">
        <button class="btn btn-outline-primary" onclick="addQuestion()">
          <i class="fas fa-plus"></i> Add Question
        </button>
      </div>
    </div>
    <div class="mapping-grid" id="mappingGrid">
      <!-- Questions will be added here -->
    </div>
  </section>

  <!-- Preview Section -->
  <section class="preview-section glass-card">
    <div class="section-header">
      <h3><i class="fas fa-eye"></i> Preview</h3>
      <div class="preview-actions">
        <button class="btn btn-outline-primary" onclick="testMapping()">
          <i class="fas fa-vial"></i> Test Mapping
        </button>
      </div>
    </div>
    <div class="preview-content" id="previewContent">
      <!-- Preview content will be shown here -->
    </div>
  </section>
</div>
{% endblock %} {% block scripts %}
<script>
  // File Upload Handling
  const dragDropArea = document.getElementById("dragDropArea");
  const fileInput = document.getElementById("fileInput");
  const uploadProgress = document.getElementById("uploadProgress");
  const progressFill = uploadProgress.querySelector(".progress-fill");
  const progressPercent = document.getElementById("progressPercent");
  const uploadedFiles = document.getElementById("uploadedFiles");

  // Prevent default drag behaviors
  ["dragenter", "dragover", "dragleave", "drop"].forEach((eventName) => {
    dragDropArea.addEventListener(eventName, preventDefaults, false);
    document.body.addEventListener(eventName, preventDefaults, false);
  });

  function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
  }

  // Highlight drop zone when dragging over it
  ["dragenter", "dragover"].forEach((eventName) => {
    dragDropArea.addEventListener(eventName, () => {
      dragDropArea.classList.add("dragging");
    });
  });

  ["dragleave", "drop"].forEach((eventName) => {
    dragDropArea.addEventListener(eventName, () => {
      dragDropArea.classList.remove("dragging");
    });
  });

  // Handle dropped files
  dragDropArea.addEventListener("drop", handleDrop);
  fileInput.addEventListener("change", handleFileSelect);

  function handleDrop(e) {
    const files = e.dataTransfer.files;
    handleFiles(files);
  }

  function handleFileSelect(e) {
    const files = e.target.files;
    handleFiles(files);
  }

  function handleFiles(files) {
    uploadProgress.style.display = "block";
    const formData = new FormData();

    Array.from(files).forEach((file) => {
      formData.append("files[]", file);
    });

    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 5;
      updateProgress(progress);
      if (progress >= 100) {
        clearInterval(interval);
        setTimeout(() => {
          uploadProgress.style.display = "none";
          addUploadedFiles(files);
        }, 500);
      }
    }, 100);

    // TODO: Implement actual file upload
    fetch("/upload", {
      method: "POST",
      body: formData,
    })
      .then((response) => response.json())
      .then((data) => {
        console.log("Upload successful:", data);
      })
      .catch((error) => {
        console.error("Upload failed:", error);
      });
  }

  function updateProgress(percent) {
    progressFill.style.width = `${percent}%`;
    progressPercent.textContent = `${percent}%`;
  }

  function addUploadedFiles(files) {
    Array.from(files).forEach((file) => {
      const fileItem = document.createElement("div");
      fileItem.className = "file-item";
      fileItem.innerHTML = `
            <i class="fas fa-file-alt"></i>
            <span class="file-name">${file.name}</span>
            <button class="btn btn-icon" onclick="removeFile(this)">
                <i class="fas fa-times"></i>
            </button>
        `;
      uploadedFiles.appendChild(fileItem);
    });
  }

  function removeFile(button) {
    button.closest(".file-item").remove();
  }

  // Mapping Configuration
  function addQuestion() {
    const mappingGrid = document.getElementById("mappingGrid");
    const questionCount = mappingGrid.children.length + 1;

    const questionCard = document.createElement("div");
    questionCard.className = "question-card glass-card";
    questionCard.innerHTML = `
        <div class="question-header">
            <h4>Question ${questionCount}</h4>
            <button class="btn btn-icon" onclick="removeQuestion(this)">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="question-content">
            <div class="form-group">
                <label>Question Type</label>
                <select class="form-control" onchange="updateQuestionFields(this)">
                    <option value="multiple_choice">Multiple Choice</option>
                    <option value="written">Written Answer</option>
                </select>
            </div>
            <div class="form-group">
                <label>Correct Answer</label>
                <input type="text" class="form-control" placeholder="Enter correct answer">
            </div>
            <div class="form-group points-group">
                <label>Points</label>
                <input type="number" class="form-control" min="0" value="1">
            </div>
        </div>
    `;

    mappingGrid.appendChild(questionCard);
    questionCard.style.animation = "fadeInUp 0.3s ease forwards";
  }

  function removeQuestion(button) {
    const card = button.closest(".question-card");
    card.style.animation = "fadeOutDown 0.3s ease forwards";
    setTimeout(() => card.remove(), 300);
  }

  function updateQuestionFields(select) {
    const content = select.closest(".question-content");
    const answerField = content.querySelector('input[type="text"]');

    if (select.value === "multiple_choice") {
      answerField.placeholder = "Enter correct option (A, B, C, D)";
    } else {
      answerField.placeholder = "Enter correct answer or keywords";
    }
  }

  function saveMapping() {
    showLoading();
    // TODO: Implement mapping save
    setTimeout(() => {
      hideLoading();
      // Show success notification
      const toast = document.createElement("div");
      toast.className = "toast success";
      toast.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>Mapping saved successfully</span>
        `;
      document.querySelector(".toast-container").appendChild(toast);
      setTimeout(() => toast.remove(), 3000);
    }, 1000);
  }

  function resetMapping() {
    if (
      confirm(
        "Are you sure you want to reset the mapping? All changes will be lost."
      )
    ) {
      document.getElementById("mappingGrid").innerHTML = "";
      document.getElementById("uploadedFiles").innerHTML = "";
      document.getElementById("fileInput").value = "";
      document.getElementById("totalQuestions").value = "";
      document.getElementById("passingScore").value = "60";
    }
  }

  function testMapping() {
    showLoading();
    // TODO: Implement mapping test
    setTimeout(() => {
      hideLoading();
      const previewContent = document.getElementById("previewContent");
      previewContent.innerHTML = `
            <div class="test-result success">
                <i class="fas fa-check-circle"></i>
                <h4>Mapping Test Successful</h4>
                <p>All questions were successfully mapped to the answer template.</p>
            </div>
        `;
    }, 1500);
  }

  // Initialize
  document.addEventListener("DOMContentLoaded", () => {
    // Add initial question
    addQuestion();
  });
</script>
{% endblock %}
