"""
LLM Performance Configuration

This module contains optimized settings for faster LLM processing.
Adjust these values to balance speed vs accuracy based on your needs.
"""

# Model Selection (faster models)
FAST_MODEL = "deepseek-chat"  # Faster than deepseek-reasoner
REASONING_MODEL = "deepseek-reasoner"  # More accurate but slower

# Performance Settings
FAST_TEMPERATURE = 0.1  # Slightly higher for faster responses
ACCURATE_TEMPERATURE = 0.0  # Lower for more deterministic responses

# Timeout Settings (in seconds)
FAST_TIMEOUT = 15.0  # Quick timeout for fast responses
STANDARD_TIMEOUT = 30.0  # Standard timeout
LONG_TIMEOUT = 60.0  # For complex operations

# Token Limits
SMALL_RESPONSE = 200  # For simple tasks like guide type determination
MEDIUM_RESPONSE = 500  # For grading individual questions
LARGE_RESPONSE = 1000  # For complex mapping operations
MAX_RESPONSE = 2000  # Maximum for very complex tasks

# Content Limits (characters)
GUIDE_TYPE_CONTENT_LIMIT = 1500  # For guide type determination
MAPPING_CONTENT_LIMIT = 3000  # For mapping operations
FULL_CONTENT_LIMIT = 5000  # For full document processing

# Retry Settings
FAST_MAX_RETRIES = 1  # Quick failure for speed
STANDARD_MAX_RETRIES = 2  # Standard retry count
ROBUST_MAX_RETRIES = 3  # More retries for reliability

FAST_RETRY_DELAY = 0.5  # Quick retry delay
STANDARD_RETRY_DELAY = 1.0  # Standard retry delay
ROBUST_RETRY_DELAY = 2.0  # Longer retry delay

# Performance Profiles
SPEED_PROFILE = {
    "model": FAST_MODEL,
    "temperature": FAST_TEMPERATURE,
    "timeout": FAST_TIMEOUT,
    "max_tokens": MEDIUM_RESPONSE,
    "max_retries": FAST_MAX_RETRIES,
    "retry_delay": FAST_RETRY_DELAY,
    "deterministic": False,
    "seed": None,
    "content_limit": MAPPING_CONTENT_LIMIT
}

BALANCED_PROFILE = {
    "model": FAST_MODEL,
    "temperature": FAST_TEMPERATURE,
    "timeout": STANDARD_TIMEOUT,
    "max_tokens": LARGE_RESPONSE,
    "max_retries": STANDARD_MAX_RETRIES,
    "retry_delay": STANDARD_RETRY_DELAY,
    "deterministic": False,
    "seed": None,
    "content_limit": MAPPING_CONTENT_LIMIT
}

ACCURACY_PROFILE = {
    "model": REASONING_MODEL,
    "temperature": ACCURATE_TEMPERATURE,
    "timeout": LONG_TIMEOUT,
    "max_tokens": LARGE_RESPONSE,
    "max_retries": ROBUST_MAX_RETRIES,
    "retry_delay": ROBUST_RETRY_DELAY,
    "deterministic": True,
    "seed": 42,
    "content_limit": FULL_CONTENT_LIMIT
}

# Default profile (optimized for speed)
DEFAULT_PROFILE = SPEED_PROFILE

def get_profile(profile_name: str = "speed") -> dict:
    """
    Get a performance profile by name.
    
    Args:
        profile_name: One of "speed", "balanced", "accuracy"
        
    Returns:
        dict: Performance configuration
    """
    profiles = {
        "speed": SPEED_PROFILE,
        "balanced": BALANCED_PROFILE,
        "accuracy": ACCURACY_PROFILE
    }
    
    return profiles.get(profile_name.lower(), DEFAULT_PROFILE)

def get_task_settings(task_type: str) -> dict:
    """
    Get optimized settings for specific task types.
    
    Args:
        task_type: One of "guide_type", "mapping", "grading", "test"
        
    Returns:
        dict: Task-specific settings
    """
    task_settings = {
        "guide_type": {
            "max_tokens": SMALL_RESPONSE,
            "timeout": FAST_TIMEOUT,
            "content_limit": GUIDE_TYPE_CONTENT_LIMIT
        },
        "mapping": {
            "max_tokens": LARGE_RESPONSE,
            "timeout": STANDARD_TIMEOUT,
            "content_limit": MAPPING_CONTENT_LIMIT
        },
        "grading": {
            "max_tokens": MEDIUM_RESPONSE,
            "timeout": STANDARD_TIMEOUT,
            "content_limit": MAPPING_CONTENT_LIMIT
        },
        "test": {
            "max_tokens": SMALL_RESPONSE,
            "timeout": FAST_TIMEOUT,
            "content_limit": 500
        }
    }
    
    return task_settings.get(task_type.lower(), {
        "max_tokens": MEDIUM_RESPONSE,
        "timeout": STANDARD_TIMEOUT,
        "content_limit": MAPPING_CONTENT_LIMIT
    })
