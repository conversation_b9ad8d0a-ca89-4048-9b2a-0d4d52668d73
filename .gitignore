# Environment variables and secrets
.env
.env.*
config.env
config.env.*
!config.env.example

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.venv/

# IDE
.idea/
.vscode/
*.swp
*.swo
*.swn
*.bak

# Project specific
temp/
output/
logs/
*.log
!.gitkeep

# Test coverage and cache
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/
.ruff_cache/

# Documentation
docs/_build/
docs/_static/
docs/_templates/

# OS
.DS_Store
Thumbs.db
Desktop.ini

# Temporary files
*.tmp
*.temp
~*
*~ 