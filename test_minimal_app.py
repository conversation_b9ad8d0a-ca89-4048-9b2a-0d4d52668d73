#!/usr/bin/env python3
"""
Minimal Flask app to test upload functionality.
"""

import os
import sys
from flask import Flask, request, render_template_string, redirect, url_for, flash
from werkzeug.utils import secure_filename

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

app = Flask(__name__)
app.secret_key = 'test-secret-key'
app.config['UPLOAD_FOLDER'] = 'temp/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

ALLOWED_EXTENSIONS = {'txt', 'pdf', 'docx', 'jpg', 'jpeg', 'png', 'tiff', 'bmp'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Simple HTML template for testing
TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Upload Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .upload-area { border: 2px dashed #ccc; padding: 20px; margin: 20px 0; text-align: center; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Upload Functionality Test</h1>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <h2>Upload Guide</h2>
    <form action="/upload_guide" method="post" enctype="multipart/form-data">
        <div class="upload-area">
            <input type="file" name="file" accept=".txt,.pdf,.docx" required>
            <br><br>
            <button type="submit">Upload Guide</button>
        </div>
    </form>
    
    <h2>Upload Submission</h2>
    <form action="/upload_submission" method="post" enctype="multipart/form-data">
        <div class="upload-area">
            <input type="file" name="file" accept=".txt,.pdf,.docx,.jpg,.jpeg,.png,.tiff,.bmp" required>
            <br><br>
            <button type="submit">Upload Submission</button>
        </div>
    </form>
    
    <h2>Status</h2>
    <p>Guide uploaded: {{ guide_status }}</p>
    <p>Submission uploaded: {{ submission_status }}</p>
</body>
</html>
'''

@app.route('/')
def index():
    guide_status = "Yes" if os.path.exists(os.path.join(app.config['UPLOAD_FOLDER'], 'guide.txt')) else "No"
    submission_status = "Yes" if os.path.exists(os.path.join(app.config['UPLOAD_FOLDER'], 'submission.txt')) else "No"
    
    return render_template_string(TEMPLATE, 
                                guide_status=guide_status, 
                                submission_status=submission_status)

@app.route('/upload_guide', methods=['POST'])
def upload_guide():
    if 'file' not in request.files:
        flash('No file selected', 'error')
        return redirect(url_for('index'))
    
    file = request.files['file']
    if file.filename == '':
        flash('No file selected', 'error')
        return redirect(url_for('index'))
    
    if file and allowed_file(file.filename):
        try:
            # Save the file
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], 'guide.txt')
            file.save(filepath)
            
            flash(f'Guide uploaded successfully: {filename}', 'success')
        except Exception as e:
            flash(f'Upload failed: {str(e)}', 'error')
    else:
        flash('Invalid file type', 'error')
    
    return redirect(url_for('index'))

@app.route('/upload_submission', methods=['POST'])
def upload_submission():
    if 'file' not in request.files:
        flash('No file selected', 'error')
        return redirect(url_for('index'))
    
    file = request.files['file']
    if file.filename == '':
        flash('No file selected', 'error')
        return redirect(url_for('index'))
    
    if file and allowed_file(file.filename):
        try:
            # Save the file
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], 'submission.txt')
            file.save(filepath)
            
            flash(f'Submission uploaded successfully: {filename}', 'success')
        except Exception as e:
            flash(f'Upload failed: {str(e)}', 'error')
    else:
        flash('Invalid file type', 'error')
    
    return redirect(url_for('index'))

if __name__ == '__main__':
    print("🧪 Starting Minimal Upload Test Server...")
    print("📱 Test page: http://localhost:5001")
    print("🛑 Press Ctrl+C to stop the server")
    app.run(debug=True, host='0.0.0.0', port=5001)
