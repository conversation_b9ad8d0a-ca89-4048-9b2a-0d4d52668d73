{% extends "base.html" %} {% block title %}Student Submissions - <PERSON>am Grader{%
endblock %} {% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
  <h1 class="mb-0">
    <i class="bi bi-file-text text-info me-2"></i>
    Student Submissions {% if total_submissions > 0 %}
    <span class="badge bg-info ms-2">{{ total_submissions }}</span>
    {% endif %}
  </h1>
  <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
    <i class="bi bi-arrow-left me-1"></i> Back to Home
  </a>
</div>

<div class="card mb-4">
  <div class="card-header bg-info text-white">
    <h5 class="mb-0">Submission Details</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-4 mb-3">
        <div class="p-3 bg-light rounded">
          <h6 class="mb-1">Filename</h6>
          <div class="text-break">{{ submission.filename }}</div>
        </div>
      </div>
      <div class="col-md-4 mb-3">
        <div class="p-3 bg-light rounded">
          <h6 class="mb-1">Upload Time</h6>
          <div>{{ submission.upload_time|format_date }}</div>
        </div>
      </div>
      <div class="col-md-4 mb-3">
        <div class="p-3 bg-light rounded">
          <h6 class="mb-1">Size</h6>
          <div>{{ "%.2f"|format(submission.file_size / 1024) }} KB</div>
        </div>
      </div>
    </div>
  </div>
</div>

{% if submissions|length > 0 %}
<div class="card mb-4">
  <div
    class="card-header bg-info text-white d-flex justify-content-between align-items-center"
  >
    <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>All Submissions</h5>
    <div>
      <button
        type="button"
        class="btn btn-sm btn-outline-light"
        data-bs-toggle="modal"
        data-bs-target="#uploadSubmissionModal"
      >
        <i class="bi bi-plus-circle me-1"></i> Add Submission
      </button>
    </div>
  </div>
  <div class="card-body p-0">
    <div class="list-group list-group-flush">
      {% for sub in submissions %}
      <div
        class="list-group-item d-flex justify-content-between align-items-center {% if sub.filename == submission.filename %}active{% endif %}"
      >
        <a
          href="{{ url_for('view_submission', id=sub.filename) }}"
          class="d-flex align-items-center text-decoration-none flex-grow-1 {% if sub.filename == submission.filename %}text-white{% else %}text-dark{% endif %}"
        >
          <i class="bi bi-file-earmark-text me-2"></i>
          <div>
            <div class="fw-bold">{{ sub.filename }}</div>
            <small
              class="{% if sub.filename == submission.filename %}text-white-50{% else %}text-muted{% endif %}"
            >
              {{ sub.upload_time|format_date }} | {{
              "%.1f"|format(sub.raw_text|length / 1024) }} KB
            </small>
          </div>
        </a>
        <div class="ms-2">
          <form
            action="{{ url_for('remove_submission', filename=sub.filename) }}"
            method="post"
            class="d-inline"
          >
            <button
              type="submit"
              class="btn btn-sm {% if sub.filename == submission.filename %}btn-outline-light{% else %}btn-outline-danger{% endif %}"
              data-confirm="Are you sure you want to remove this submission?"
            >
              <i class="bi bi-trash"></i>
            </button>
          </form>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</div>

<!-- Upload Submission Modal -->
<div
  class="modal fade"
  id="uploadSubmissionModal"
  tabindex="-1"
  aria-labelledby="uploadSubmissionModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="uploadSubmissionModalLabel">
          Upload Submission
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <form
        action="{{ url_for('upload_submission') }}"
        method="post"
        enctype="multipart/form-data"
      >
        <div class="modal-body">
          <div class="mb-3">
            <label for="submissionFile" class="form-label"
              >Select Submission File</label
            >
            <input
              class="form-control"
              type="file"
              id="submissionFile"
              name="file"
              accept=".txt,.pdf,.docx,.doc,.rtf,.odt,.html,.htm,.md,.jpg,.jpeg,.png"
              required
            />
            <div class="form-text">
              Supported formats: TXT, PDF, DOCX, DOC, RTF, ODT, HTML, MD, JPG,
              PNG
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-upload me-1"></i> Upload
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endif %}

<div class="card">
  <div
    class="card-header bg-info text-white d-flex justify-content-between align-items-center"
  >
    <div>
      <h5 class="mb-0">Raw Submission Content</h5>
      <small class="text-white-50"
        >{{ submission.raw_text|length }} characters extracted</small
      >
    </div>
    <div class="d-flex">
      <button
        type="button"
        class="btn btn-sm btn-outline-light me-2"
        data-bs-toggle="modal"
        data-bs-target="#uploadSubmissionModal"
      >
        <i class="bi bi-plus-circle me-1"></i> Add Submission
      </button>
      <form
        action="{{ url_for('clear_submission') }}"
        method="post"
        class="d-inline"
      >
        <button
          type="submit"
          class="btn btn-sm btn-outline-light"
          data-confirm="Are you sure you want to clear ALL submissions? This cannot be undone."
        >
          <i class="bi bi-trash me-1"></i> Clear All
        </button>
      </form>
    </div>
  </div>
  <div class="card-body">
    <div class="bg-light p-3 rounded submission-content">
      {{ submission.raw_text|nl2br }}
    </div>
  </div>
</div>

<div class="d-flex justify-content-between mt-4">
  <a href="{{ url_for('index') }}" class="btn btn-secondary">
    <i class="bi bi-arrow-left me-1"></i> Back to Home
  </a>

  <div>
    {% if session.get('guide_uploaded') %}
    <a href="{{ url_for('view_guide') }}" class="btn btn-primary">
      <i class="bi bi-journal-text me-1"></i> View Guide
    </a>

    <form
      action="{{ url_for('map_submission') }}"
      method="post"
      class="d-inline"
    >
      <div class="d-inline-block me-2">
        <div class="input-group">
          <span class="input-group-text">Questions:</span>
          <input
            type="number"
            class="form-control"
            name="num_questions"
            min="1"
            value="{{ session.get('num_questions', 1) }}"
            style="width: 70px"
          />
          <button type="submit" class="btn btn-success">
            <i class="bi bi-diagram-3 me-1"></i> Map Submission
          </button>
        </div>
      </div>
    </form>

    <form
      action="{{ url_for('grade_submission') }}"
      method="post"
      class="d-inline"
    >
      <button type="submit" class="btn btn-primary ms-2">
        <i class="bi bi-award me-1"></i> Grade Submission
      </button>
    </form>
    {% endif %}
  </div>
</div>

{% endblock %} {% block extra_css %}
<style>
  .submission-content {
    max-height: 60vh;
    overflow-y: auto;
    font-family: var(--font-mono);
    line-height: 1.6;
  }

  .input-group {
    width: auto;
    display: inline-flex;
  }

  .input-group input[type="number"] {
    width: 70px;
  }
</style>
{% endblock %} {% block extra_js %}
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Add confirmation for buttons
    document.querySelectorAll("[data-confirm]").forEach((button) => {
      button.addEventListener("click", function (e) {
        if (!confirm(this.getAttribute("data-confirm"))) {
          e.preventDefault();
        }
      });
    });
  });
</script>
{% endblock %}
