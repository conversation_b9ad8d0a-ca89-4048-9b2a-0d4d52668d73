{% extends "base.html" %} {% block title %}Grading Results{% endblock %} {%
block header %}Grading Results{% endblock %} {% block header_actions %}
<div class="header-actions">
  <button class="btn btn-primary" onclick="downloadResults()">
    <i class="fas fa-download"></i> Download Results
  </button>
</div>
{% endblock %} {% block content %}
<div class="results-container">
  <!-- Results Summary -->
  <div class="grid">
    <div class="stat-card glass-card result-card">
      <i class="fas fa-users"></i>
      <div class="stat-content">
        <h3>Total Students</h3>
        <p class="stat-value">{{ total_students }}</p>
      </div>
    </div>
    <div class="stat-card glass-card result-card">
      <i class="fas fa-check-circle"></i>
      <div class="stat-content">
        <h3>Average Score</h3>
        <p class="stat-value">{{ "%.2f"|format(average_score) }}%</p>
      </div>
    </div>
    <div class="stat-card glass-card result-card">
      <i class="fas fa-chart-line"></i>
      <div class="stat-content">
        <h3>Pass Rate</h3>
        <p class="stat-value">{{ "%.2f"|format(pass_rate) }}%</p>
      </div>
    </div>
  </div>

  <!-- Results Table -->
  <div class="table-container glass-card">
    <div class="table-header">
      <h2>Detailed Results</h2>
      <div class="table-actions">
        <input
          type="text"
          id="searchInput"
          placeholder="Search students..."
          class="search-input"
        />
        <select id="sortSelect" class="select-input">
          <option value="name">Sort by Name</option>
          <option value="score">Sort by Score</option>
          <option value="status">Sort by Status</option>
        </select>
      </div>
    </div>
    <table class="table-modern">
      <thead>
        <tr>
          <th>Student ID</th>
          <th>Name</th>
          <th>Score</th>
          <th>Status</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for result in results %}
        <tr class="result-row">
          <td>{{ result.student_id }}</td>
          <td>{{ result.name }}</td>
          <td>
            <div class="score-bar">
              <div
                class="score-progress"
                style="width: {{ result.score }}%"
              ></div>
              <span>{{ "%.2f"|format(result.score) }}%</span>
            </div>
          </td>
          <td>
            <span
              class="status-badge {{ 'pass' if result.score >= passing_score else 'fail' }}"
            >
              {{ 'PASS' if result.score >= passing_score else 'FAIL' }}
            </span>
          </td>
          <td>
            <button
              class="btn btn-icon"
              onclick="viewDetails('{{ result.student_id }}')"
            >
              <i class="fas fa-eye"></i>
            </button>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<!-- Detail Modal -->
<div id="detailModal" class="modal">
  <div class="modal-content glass-card">
    <div class="modal-header">
      <h2>Detailed Results</h2>
      <button class="close-btn" onclick="closeModal()">&times;</button>
    </div>
    <div class="modal-body">
      <!-- Content will be loaded dynamically -->
    </div>
  </div>
</div>
{% endblock %} {% block scripts %}
<script>
  // Search functionality
  document
    .getElementById("searchInput")
    .addEventListener("input", function (e) {
      const searchTerm = e.target.value.toLowerCase();
      document.querySelectorAll(".result-row").forEach((row) => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? "" : "none";
      });
    });

  // Sorting functionality
  document
    .getElementById("sortSelect")
    .addEventListener("change", function (e) {
      const sortBy = e.target.value;
      const tbody = document.querySelector("tbody");
      const rows = Array.from(tbody.querySelectorAll("tr"));

      rows.sort((a, b) => {
        let aVal, bVal;
        if (sortBy === "name") {
          aVal = a.cells[1].textContent;
          bVal = b.cells[1].textContent;
          return aVal.localeCompare(bVal);
        } else if (sortBy === "score") {
          aVal = parseFloat(a.cells[2].textContent);
          bVal = parseFloat(b.cells[2].textContent);
          return bVal - aVal;
        } else {
          aVal = a.cells[3].textContent;
          bVal = b.cells[3].textContent;
          return aVal.localeCompare(bVal);
        }
      });

      rows.forEach((row) => tbody.appendChild(row));
    });

  // Download results as Excel
  function downloadResults() {
    showLoading();
    fetch("/download-results")
      .then((response) => response.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "exam_results.xlsx";
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        hideLoading();
      })
      .catch((error) => {
        console.error("Error downloading results:", error);
        hideLoading();
      });
  }

  // View detail modal
  function viewDetails(studentId) {
    showLoading();
    fetch(`/student-details/${studentId}`)
      .then((response) => response.json())
      .then((data) => {
        document.querySelector(".modal-body").innerHTML = `
                    <div class="student-details">
                        <h3>${data.name}</h3>
                        <p>Student ID: ${data.student_id}</p>
                        <p>Total Score: ${data.score}%</p>
                        <div class="answer-details">
                            <h4>Answers Breakdown</h4>
                            <div class="answer-grid">
                                ${data.answers
                                  .map(
                                    (answer) => `
                                    <div class="answer-card ${
                                      answer.correct ? "correct" : "incorrect"
                                    }">
                                        <strong>Question ${
                                          answer.question
                                        }</strong>
                                        <p>Answer: ${answer.given_answer}</p>
                                        <p>Correct: ${answer.correct_answer}</p>
                                    </div>
                                `
                                  )
                                  .join("")}
                            </div>
                        </div>
                    </div>
                `;
        document.getElementById("detailModal").style.display = "flex";
        hideLoading();
      })
      .catch((error) => {
        console.error("Error fetching details:", error);
        hideLoading();
      });
  }

  function closeModal() {
    document.getElementById("detailModal").style.display = "none";
  }

  // Close modal when clicking outside
  window.onclick = function (event) {
    const modal = document.getElementById("detailModal");
    if (event.target === modal) {
      modal.style.display = "none";
    }
  };
</script>
{% endblock %}
