{% extends "base.html" %} {% block title %}Batch Grading Results - Exam Grader{%
endblock %} {% block content %}
<div class="page-header">
  <h1><i class="bi bi-layers-fill"></i> Batch Grading Results</h1>
  <p class="page-subtitle">
    Review and analyze results from multiple student submissions
  </p>
  <div class="header-actions">
    <a href="{{ url_for('download_batch_excel') }}" class="btn btn-primary">
      <i class="bi bi-file-earmark-excel"></i> Export All to Excel
    </a>
    <button class="btn btn-secondary" onclick="location.reload()">
      <i class="bi bi-arrow-clockwise"></i> Refresh
    </button>
  </div>
</div>
<div class="batch-container">
  <!-- Summary Statistics -->
  <div class="dashboard-grid">
    <div class="row g-3 mb-4">
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="bi bi-files"></i>
          </div>
          <div class="stat-content">
            <h3>Total Files</h3>
            <div class="stat-value">{{ summary.total_submissions }}</div>
            <div class="stat-label">Submissions processed</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="bi bi-check-circle"></i>
          </div>
          <div class="stat-content">
            <h3>Average Score</h3>
            <div class="stat-value stat-primary">
              {{ summary.average_score|round|int }}%
            </div>
            <div class="stat-label">Class average</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="bi bi-graph-up"></i>
          </div>
          <div class="stat-content">
            <h3>Highest Score</h3>
            <div class="stat-value stat-success">
              {{ summary.highest_score|round|int }}%
            </div>
            <div class="stat-label">Best performance</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="bi bi-mortarboard"></i>
          </div>
          <div class="stat-content">
            <h3>Pass Rate</h3>
            <div class="stat-value stat-warning">
              {{ ((summary.grade_distribution['A+'] +
              summary.grade_distribution['A'] + summary.grade_distribution['B'])
              / summary.total_submissions * 100)|round|int }}%
            </div>
            <div class="stat-label">A & B grades</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Grade Distribution -->
    <div class="row g-3 mb-4">
      <div class="col-12">
        <div class="main-card compact-card">
          <div class="card-header">
            <h4><i class="bi bi-bar-chart"></i> Grade Distribution</h4>
            <p class="card-subtitle">Visual breakdown of student performance</p>
          </div>
          <div class="card-content">
            <div class="distribution-chart">
              {% set total = summary.total_submissions %} {% if total > 0 %} {%
              for grade, count in summary.grade_distribution.items() %} {% set
              percent = (count / total * 100)|round|int %}
              <div class="distribution-bar">
                <div
                  class="bar-fill grade-{{ grade|lower|replace('+', 'plus') }}"
                  style="height: {{ percent }}%"
                >
                  <span class="bar-value">{{ count }}</span>
                </div>
                <span class="bar-label">{{ grade }}</span>
              </div>
              {% endfor %} {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Results Table -->
    <div class="results-section glass-card">
      <div class="section-header">
        <h3><i class="fas fa-table"></i> Individual Results</h3>
        <div class="header-actions">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input
              type="text"
              id="searchInput"
              placeholder="Search results..."
            />
          </div>
          <select id="gradeFilter" class="select-input">
            <option value="all">All Grades</option>
            <option value="A+">A+</option>
            <option value="A">A</option>
            <option value="B">B</option>
            <option value="C">C</option>
            <option value="D">D</option>
            <option value="F">F</option>
          </select>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table-modern">
          <thead>
            <tr>
              <th>Student ID</th>
              <th>Score</th>
              <th>Grade</th>
              <th>Points</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="results-table-body">
            {% for result in results %}
            <tr class="result-row" data-grade="{{ result.letter_grade }}">
              <td>
                <div class="student-info">
                  <i class="fas fa-user-graduate"></i>
                  <div>
                    <strong>{{ result.submission_id }}</strong>
                    <small
                      >{{ result.metadata.graded_at|default('N/A', true)
                      }}</small
                    >
                  </div>
                </div>
              </td>
              <td>
                <div class="score-bar">
                  <div
                    class="score-progress"
                    style="width: {{ result.percent_score }}%"
                  ></div>
                  <span>{{ result.percent_score|round|int }}%</span>
                </div>
              </td>
              <td>
                <span class="grade-badge {{ result.letter_grade|lower }}"
                  >{{ result.letter_grade }}</span
                >
              </td>
              <td>
                <div class="points-info">
                  <span class="current">{{ result.overall_score }}</span>
                  <span class="separator">/</span>
                  <span class="total">{{ result.max_possible_score }}</span>
                </div>
              </td>
              <td>
                <button
                  class="btn btn-icon"
                  onclick="viewDetails('{{ loop.index0 }}')"
                >
                  <i class="fas fa-eye"></i>
                </button>
                <button
                  class="btn btn-icon"
                  onclick="downloadResult('{{ loop.index0 }}')"
                >
                  <i class="fas fa-download"></i>
                </button>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      <div class="table-footer">
        <div class="results-info">
          Showing <span id="showing-start">1</span> to
          <span id="showing-end">{{ results|length }}</span> of
          <span id="total-results">{{ results|length }}</span> results
        </div>
        <div class="pagination" id="pagination">
          <!-- Pagination will be added by JavaScript -->
        </div>
      </div>
    </div>
  </div>

  <!-- Detail Modal -->
  <div id="detailModal" class="modal">
    <div class="modal-content glass-card">
      <div class="modal-header">
        <h2>Detailed Results</h2>
        <button class="close-btn" onclick="closeModal()">&times;</button>
      </div>
      <div class="modal-body">
        <div class="student-overview">
          <div class="student-header">
            <div class="student-id"></div>
            <div class="student-score"></div>
          </div>
          <div class="score-breakdown">
            <h3>Score Breakdown</h3>
            <div class="criteria-grid">
              <!-- Will be populated by JavaScript -->
            </div>
          </div>
          <div class="feedback-section">
            <div class="strengths">
              <h3>Strengths</h3>
              <ul class="feedback-list">
                <!-- Will be populated by JavaScript -->
              </ul>
            </div>
            <div class="improvements">
              <h3>Areas for Improvement</h3>
              <ul class="feedback-list">
                <!-- Will be populated by JavaScript -->
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="closeModal()">Close</button>
        <button class="btn btn-primary" onclick="viewFullReport()">
          <i class="fas fa-file-alt"></i> View Full Report
        </button>
      </div>
    </div>
  </div>
  {% endblock %} {% block scripts %}
  <script>
    const resultsData = {{ results|tojson|safe }};
    let currentPage = 1;
    const resultsPerPage = 10;

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
        setupFilters();
        setupPagination();
        updateResultsDisplay();
    });

    // Setup filters
    function setupFilters() {
        const searchInput = document.getElementById('searchInput');
        const gradeFilter = document.getElementById('gradeFilter');

        searchInput.addEventListener('input', () => {
            currentPage = 1;
            updateResultsDisplay();
        });

        gradeFilter.addEventListener('change', () => {
            currentPage = 1;
            updateResultsDisplay();
        });
    }

    // Filter results
    function filterResults() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const gradeFilter = document.getElementById('gradeFilter').value;

        return resultsData.filter(result => {
            const matchesSearch = result.submission_id.toLowerCase().includes(searchTerm);
            const matchesGrade = gradeFilter === 'all' || result.letter_grade === gradeFilter;
            return matchesSearch && matchesGrade;
        });
    }

    // Update results display
    function updateResultsDisplay() {
        const filteredResults = filterResults();
        const startIndex = (currentPage - 1) * resultsPerPage;
        const endIndex = Math.min(startIndex + resultsPerPage, filteredResults.length);
        const pageResults = filteredResults.slice(startIndex, endIndex);

        const tbody = document.getElementById('results-table-body');
        tbody.innerHTML = pageResults.map((result, index) => `
            <tr class="result-row" data-grade="${result.letter_grade}">
                <td>
                    <div class="student-info">
                        <i class="fas fa-user-graduate"></i>
                        <div>
                            <strong>${result.submission_id}</strong>
                            <small>${result.metadata?.graded_at || 'N/A'}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="score-bar">
                        <div class="score-progress" style="width: ${result.percent_score}%"></div>
                        <span>${Math.round(result.percent_score)}%</span>
                    </div>
                </td>
                <td>
                    <span class="grade-badge ${result.letter_grade.toLowerCase()}">${result.letter_grade}</span>
                </td>
                <td>
                    <div class="points-info">
                        <span class="current">${result.overall_score}</span>
                        <span class="separator">/</span>
                        <span class="total">${result.max_possible_score}</span>
                    </div>
                </td>
                <td>
                    <button class="btn btn-icon" onclick="viewDetails('${startIndex + index}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-icon" onclick="downloadResult('${startIndex + index}')">
                        <i class="fas fa-download"></i>
                    </button>
                </td>
            </tr>
        `).join('');

        updatePagination(filteredResults.length);
        updateResultsInfo(startIndex + 1, endIndex, filteredResults.length);
    }

    // Update pagination
    function updatePagination(totalResults) {
        const totalPages = Math.ceil(totalResults / resultsPerPage);
        const pagination = document.getElementById('pagination');

        let pages = [];
        if (totalPages <= 7) {
            pages = Array.from({length: totalPages}, (_, i) => i + 1);
        } else {
            if (currentPage <= 4) {
                pages = [1, 2, 3, 4, 5, '...', totalPages];
            } else if (currentPage >= totalPages - 3) {
                pages = [1, '...', totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
            } else {
                pages = [1, '...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages];
            }
        }

        pagination.innerHTML = `
            <button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
                <i class="fas fa-chevron-left"></i>
            </button>
            ${pages.map(page => `
                <button class="pagination-btn ${page === currentPage ? 'active' : ''} ${page === '...' ? 'disabled' : ''}"
                        onclick="changePage(${page === '...' ? currentPage : page})">
                    ${page}
                </button>
            `).join('')}
            <button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
    }

    function updateResultsInfo(start, end, total) {
        document.getElementById('showing-start').textContent = start;
        document.getElementById('showing-end').textContent = end;
        document.getElementById('total-results').textContent = total;
    }

    function changePage(page) {
        currentPage = page;
        updateResultsDisplay();
    }

    // View details
    function viewDetails(index) {
        const result = resultsData[index];
        const modal = document.getElementById('detailModal');

        // Update modal content
        modal.querySelector('.student-id').innerHTML = `
            <h3>${result.submission_id}</h3>
            <span class="grade-badge ${result.letter_grade.toLowerCase()}">${result.letter_grade}</span>
        `;

        modal.querySelector('.student-score').innerHTML = `
            <div class="score-circle ${getScoreClass(result.percent_score)}">
                ${Math.round(result.percent_score)}%
            </div>
        `;

        // Update criteria grid
        const criteriaGrid = modal.querySelector('.criteria-grid');
        criteriaGrid.innerHTML = result.criteria_scores?.map(criteria => `
            <div class="criteria-card">
                <h4>${criteria.description || 'Question'}</h4>
                <div class="criteria-score">
                    <span class="points">${criteria.points_earned}/${criteria.points_possible}</span>
                    <div class="score-bar">
                        <div class="score-progress" style="width: ${criteria.similarity * 100}%"></div>
                    </div>
                </div>
            </div>
        `).join('') || '<p class="no-data">No criteria scores available</p>';

        // Update feedback lists
        const strengthsList = modal.querySelector('.strengths .feedback-list');
        strengthsList.innerHTML = result.detailed_feedback?.strengths?.map(strength => `
            <li><i class="fas fa-check-circle"></i>${strength}</li>
        `).join('') || '<li class="no-data">No specific strengths identified</li>';

        const improvementsList = modal.querySelector('.improvements .feedback-list');
        improvementsList.innerHTML = result.detailed_feedback?.weaknesses?.map(weakness => `
            <li><i class="fas fa-exclamation-circle"></i>${weakness}</li>
        `).join('') || '<li class="no-data">No specific areas for improvement identified</li>';

        modal.style.display = 'flex';
    }

    function getScoreClass(score) {
        if (score >= 90) return 'excellent';
        if (score >= 80) return 'good';
        if (score >= 70) return 'fair';
        return 'poor';
    }

    function closeModal() {
        document.getElementById('detailModal').style.display = 'none';
    }

    // Download functionality
    function downloadResult(index) {
        const result = resultsData[index];
        // Implementation for downloading individual result
    }

    function downloadAllResults() {
        showLoading();
        fetch('/download-batch-results')
            .then(response => response.blob())
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'batch_results.xlsx';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                hideLoading();
            })
            .catch(error => {
                console.error('Error:', error);
                hideLoading();
                alert('Failed to download results');
            });
    }

    function refreshResults() {
        showLoading();
        location.reload();
    }

    // Close modal when clicking outside
    window.onclick = function(event) {
        const modal = document.getElementById('detailModal');
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    }
  </script>
  {% endblock %}
</div>
