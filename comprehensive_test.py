#!/usr/bin/env python3
"""
Comprehensive functionality test for the Exam Grader application.
Tests all core features including upload, navigation, settings, help, and processing.
"""

import requests
import os
import sys
import tempfile
import time
from pathlib import Path

def log(message, level="INFO"):
    """Log a message with timestamp."""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def test_server_connectivity(base_url):
    """Test if the server is running and accessible."""
    log("Testing server connectivity...")
    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            log("✅ Server is running and accessible")
            return True
        else:
            log(f"❌ Server returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        log(f"❌ Server connectivity failed: {str(e)}")
        return False

def test_page_accessibility(base_url):
    """Test accessibility of all main pages."""
    log("Testing page accessibility...")
    pages = {
        "Dashboard": "/",
        "Settings": "/settings",
        "Help": "/help",
        "View Results": "/view_results",
        "View Batch Results": "/view_batch_results",
        "Answer Mapping": "/view_mapping"
    }
    
    results = {}
    for page_name, endpoint in pages.items():
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                log(f"✅ {page_name} page accessible")
                results[page_name] = True
            else:
                log(f"❌ {page_name} page returned status {response.status_code}")
                results[page_name] = False
        except Exception as e:
            log(f"❌ {page_name} page failed: {str(e)}")
            results[page_name] = False
    
    return results

def test_upload_functionality(base_url):
    """Test file upload functionality."""
    log("Testing upload functionality...")
    
    # Create test files
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as guide_file:
        guide_file.write("""
        MARKING GUIDE - Sample Exam
        
        Question 1 (10 marks): Explain the concept of object-oriented programming
        Expected Answer: Object-oriented programming is a programming paradigm based on objects and classes.
        
        Question 2 (15 marks): What are the main principles of OOP?
        Expected Answer: Encapsulation, Inheritance, Polymorphism, and Abstraction.
        """)
        guide_path = guide_file.name
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as submission_file:
        submission_file.write("""
        Student Submission - John Doe
        
        Answer 1: Object-oriented programming is a way of programming that uses objects and classes to organize code.
        
        Answer 2: The main principles are encapsulation, inheritance, polymorphism, and abstraction.
        """)
        submission_path = submission_file.name
    
    try:
        # Test guide upload
        log("Testing guide upload...")
        with open(guide_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/upload_guide", files=files, timeout=30)
            if response.status_code in [200, 302]:
                log("✅ Guide upload successful")
                guide_success = True
            else:
                log(f"❌ Guide upload failed with status {response.status_code}")
                guide_success = False
        
        # Test submission upload
        log("Testing submission upload...")
        with open(submission_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/upload_submission", files=files, timeout=30)
            if response.status_code in [200, 302]:
                log("✅ Submission upload successful")
                submission_success = True
            else:
                log(f"❌ Submission upload failed with status {response.status_code}")
                submission_success = False
        
        return guide_success and submission_success
        
    except Exception as e:
        log(f"❌ Upload test failed: {str(e)}")
        return False
    finally:
        # Clean up test files
        try:
            os.unlink(guide_path)
            os.unlink(submission_path)
            log("✅ Test files cleaned up")
        except:
            pass

def test_processing_workflow(base_url):
    """Test the AI processing and grading workflow."""
    log("Testing AI processing workflow...")
    
    try:
        # Test grade submission endpoint
        data = {'num_questions': '1'}
        response = requests.post(f"{base_url}/grade_submission", data=data, timeout=60)
        
        if response.status_code in [200, 302]:
            log("✅ AI processing endpoint accessible")
            return True
        else:
            log(f"❌ AI processing failed with status {response.status_code}")
            return False
            
    except Exception as e:
        log(f"❌ AI processing test failed: {str(e)}")
        return False

def test_settings_functionality(base_url):
    """Test settings page functionality."""
    log("Testing settings functionality...")
    
    try:
        # Test settings page
        response = requests.get(f"{base_url}/settings", timeout=10)
        if response.status_code == 200:
            log("✅ Settings page accessible")
            
            # Test if page contains expected sections
            content = response.text.lower()
            sections = ['performance', 'file processing', 'system info', 'cache management']
            
            for section in sections:
                if section in content:
                    log(f"✅ Settings contains {section} section")
                else:
                    log(f"❌ Settings missing {section} section")
            
            return True
        else:
            log(f"❌ Settings page failed with status {response.status_code}")
            return False
            
    except Exception as e:
        log(f"❌ Settings test failed: {str(e)}")
        return False

def test_help_functionality(base_url):
    """Test help page functionality."""
    log("Testing help functionality...")
    
    try:
        response = requests.get(f"{base_url}/help", timeout=10)
        if response.status_code == 200:
            log("✅ Help page accessible")
            
            # Test if page contains expected sections
            content = response.text.lower()
            sections = ['quick start', 'features', 'faq', 'tips', 'troubleshooting']
            
            for section in sections:
                if section in content:
                    log(f"✅ Help contains {section} section")
                else:
                    log(f"❌ Help missing {section} section")
            
            return True
        else:
            log(f"❌ Help page failed with status {response.status_code}")
            return False
            
    except Exception as e:
        log(f"❌ Help test failed: {str(e)}")
        return False

def run_comprehensive_test():
    """Run all tests and provide a summary."""
    base_url = "http://127.0.0.1:8501"
    
    print("🧪 COMPREHENSIVE EXAM GRADER FUNCTIONALITY TEST")
    print("=" * 60)
    print(f"Testing application at: {base_url}")
    print("=" * 60)
    
    # Test results
    results = {}
    
    # 1. Server connectivity
    results['Server Connectivity'] = test_server_connectivity(base_url)
    
    # 2. Page accessibility
    page_results = test_page_accessibility(base_url)
    results.update(page_results)
    
    # 3. Upload functionality
    results['Upload Functionality'] = test_upload_functionality(base_url)
    
    # 4. Settings functionality
    results['Settings Functionality'] = test_settings_functionality(base_url)
    
    # 5. Help functionality
    results['Help Functionality'] = test_help_functionality(base_url)
    
    # 6. Processing workflow (if uploads successful)
    if results.get('Upload Functionality', False):
        results['AI Processing'] = test_processing_workflow(base_url)
    else:
        log("⚠️ Skipping AI processing test (uploads failed)")
        results['AI Processing'] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"TOTAL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Application is fully functional.")
        return True
    else:
        print("⚠️ Some tests failed. Check the logs above for details.")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
