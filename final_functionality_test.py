#!/usr/bin/env python3
"""
Final comprehensive functionality test for the Exam Grader application.
Tests all features with the optimized dashboard layout.
"""

import requests
import os
import tempfile
import time

def log(message, level="INFO"):
    """Log a message with timestamp."""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def test_basic_connectivity():
    """Test basic server connectivity and page loads."""
    base_url = "http://127.0.0.1:8501"
    
    log("🔗 Testing Basic Connectivity")
    print("-" * 50)
    
    # Test main pages
    pages = {
        "Dashboard": "/",
        "Settings": "/settings", 
        "Help": "/help"
    }
    
    results = {}
    for name, endpoint in pages.items():
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                log(f"✅ {name} page accessible")
                results[name] = True
            else:
                log(f"❌ {name} page failed: {response.status_code}")
                results[name] = False
        except Exception as e:
            log(f"❌ {name} page error: {str(e)}")
            results[name] = False
    
    return all(results.values())

def test_upload_workflow():
    """Test the complete upload workflow."""
    base_url = "http://127.0.0.1:8501"
    
    log("📤 Testing Upload Workflow")
    print("-" * 50)
    
    # Create test files
    guide_content = """
    SAMPLE MARKING GUIDE
    
    Question 1 (10 marks): What is Python?
    Expected Answer: Python is a high-level programming language.
    
    Question 2 (15 marks): Explain object-oriented programming.
    Expected Answer: OOP is a programming paradigm based on objects and classes.
    """
    
    submission_content = """
    STUDENT SUBMISSION - Test Student
    
    Answer 1: Python is a programming language that is easy to learn.
    
    Answer 2: Object-oriented programming uses classes and objects to organize code.
    """
    
    try:
        # Test guide upload
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(guide_content)
            guide_path = f.name
        
        log("Testing guide upload...")
        with open(guide_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/upload_guide", files=files, timeout=30)
            
        if response.status_code in [200, 302]:
            log("✅ Guide upload successful")
            guide_success = True
        else:
            log(f"❌ Guide upload failed: {response.status_code}")
            guide_success = False
        
        # Test submission upload
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(submission_content)
            submission_path = f.name
        
        log("Testing submission upload...")
        with open(submission_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/upload_submission", files=files, timeout=30)
            
        if response.status_code in [200, 302]:
            log("✅ Submission upload successful")
            submission_success = True
        else:
            log(f"❌ Submission upload failed: {response.status_code}")
            submission_success = False
        
        # Clean up
        os.unlink(guide_path)
        os.unlink(submission_path)
        
        return guide_success and submission_success
        
    except Exception as e:
        log(f"❌ Upload workflow failed: {str(e)}")
        return False

def test_dashboard_layout():
    """Test dashboard layout and statistics display."""
    base_url = "http://127.0.0.1:8501"
    
    log("📊 Testing Dashboard Layout")
    print("-" * 50)
    
    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            content = response.text.lower()
            
            # Check for stat cards
            required_elements = [
                'stat-card',
                'guide status',
                'submissions',
                'last score',
                'ai status',
                'cache usage',
                'system status'
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if not missing_elements:
                log("✅ All dashboard elements present")
                return True
            else:
                log(f"❌ Missing elements: {missing_elements}")
                return False
        else:
            log(f"❌ Dashboard failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        log(f"❌ Dashboard test failed: {str(e)}")
        return False

def test_navigation_features():
    """Test navigation and interactive features."""
    base_url = "http://127.0.0.1:8501"
    
    log("🧭 Testing Navigation Features")
    print("-" * 50)
    
    # Test settings page sections
    try:
        response = requests.get(f"{base_url}/settings", timeout=10)
        if response.status_code == 200:
            content = response.text.lower()
            settings_sections = ['performance', 'file processing', 'system info', 'cache management']
            
            missing_sections = []
            for section in settings_sections:
                if section not in content:
                    missing_sections.append(section)
            
            if not missing_sections:
                log("✅ Settings page sections complete")
                settings_success = True
            else:
                log(f"❌ Settings missing: {missing_sections}")
                settings_success = False
        else:
            log(f"❌ Settings page failed: {response.status_code}")
            settings_success = False
    except Exception as e:
        log(f"❌ Settings test failed: {str(e)}")
        settings_success = False
    
    # Test help page sections
    try:
        response = requests.get(f"{base_url}/help", timeout=10)
        if response.status_code == 200:
            content = response.text.lower()
            help_sections = ['quick start', 'features', 'faq', 'tips', 'troubleshooting']
            
            missing_sections = []
            for section in help_sections:
                if section not in content:
                    missing_sections.append(section)
            
            if not missing_sections:
                log("✅ Help page sections complete")
                help_success = True
            else:
                log(f"❌ Help missing: {missing_sections}")
                help_success = False
        else:
            log(f"❌ Help page failed: {response.status_code}")
            help_success = False
    except Exception as e:
        log(f"❌ Help test failed: {str(e)}")
        help_success = False
    
    return settings_success and help_success

def run_final_test():
    """Run the complete functionality test."""
    print("🧪 FINAL COMPREHENSIVE FUNCTIONALITY TEST")
    print("=" * 60)
    print("Testing Exam Grader with Optimized Dashboard Layout")
    print("=" * 60)
    
    # Run all tests
    tests = [
        ("Basic Connectivity", test_basic_connectivity),
        ("Upload Workflow", test_upload_workflow),
        ("Dashboard Layout", test_dashboard_layout),
        ("Navigation Features", test_navigation_features)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print()
        results[test_name] = test_func()
        print()
    
    # Summary
    print("=" * 60)
    print("📋 FINAL TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"TOTAL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print()
        print("🎉 ALL TESTS PASSED!")
        print("✅ Upload functionality working")
        print("✅ Dashboard layout optimized")
        print("✅ Navigation features complete")
        print("✅ All pages accessible")
        print("✅ Application fully functional")
        return True
    else:
        print()
        print("⚠️ Some tests failed. Check logs for details.")
        return False

if __name__ == "__main__":
    success = run_final_test()
    exit(0 if success else 1)
