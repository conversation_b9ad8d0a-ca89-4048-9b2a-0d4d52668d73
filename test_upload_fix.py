#!/usr/bin/env python3
"""
Test script to verify upload functionality is working.
"""

import os
import sys
import tempfile
from pathlib import Path

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

def test_upload_functionality():
    """Test the upload functionality components."""
    print("🧪 Testing Upload Functionality...")
    
    try:
        # Test 1: Import required modules
        print("\n1. Testing imports...")
        from webapp.app import create_app
        from src.parsing.parse_guide import parse_marking_guide
        from src.parsing.parse_submission import parse_student_submission
        print("✅ All imports successful")
        
        # Test 2: Create Flask app
        print("\n2. Testing Flask app creation...")
        app = create_app()
        print("✅ Flask app created successfully")
        
        # Test 3: Check upload folder configuration
        print("\n3. Testing upload folder configuration...")
        upload_folder = app.config.get('UPLOAD_FOLDER')
        print(f"   Upload folder: {upload_folder}")
        
        if upload_folder and os.path.exists(upload_folder):
            print("✅ Upload folder exists")
        else:
            print("⚠️  Upload folder not found, but will be created on first upload")
        
        # Test 4: Create test files
        print("\n4. Creating test files...")
        
        # Create a simple text file for testing
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("This is a test marking guide.\n\nQuestion 1: What is 2+2?\nAnswer: 4 marks\n\nQuestion 2: Explain photosynthesis.\nAnswer: 10 marks")
            test_guide_path = f.name
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Student Submission:\n\nAnswer 1: 2+2 = 4\n\nAnswer 2: Photosynthesis is the process by which plants convert sunlight into energy.")
            test_submission_path = f.name
        
        print(f"   Test guide: {test_guide_path}")
        print(f"   Test submission: {test_submission_path}")
        
        # Test 5: Test guide parsing
        print("\n5. Testing guide parsing...")
        try:
            guide, error = parse_marking_guide(test_guide_path)
            if error:
                print(f"⚠️  Guide parsing returned error: {error}")
            elif guide and guide.raw_content:
                print("✅ Guide parsing successful")
                print(f"   Content length: {len(guide.raw_content)} characters")
            else:
                print("⚠️  Guide parsing returned unexpected result")
        except Exception as e:
            print(f"❌ Guide parsing failed: {e}")
        
        # Test 6: Test submission parsing
        print("\n6. Testing submission parsing...")
        try:
            results, raw_text, error = parse_student_submission(test_submission_path)
            if error:
                print(f"⚠️  Submission parsing returned error: {error}")
            elif raw_text:
                print("✅ Submission parsing successful")
                print(f"   Content length: {len(raw_text)} characters")
            else:
                print("⚠️  Submission parsing returned unexpected result")
        except Exception as e:
            print(f"❌ Submission parsing failed: {e}")
        
        # Test 7: Test with Flask test client
        print("\n7. Testing upload routes with test client...")
        with app.test_client() as client:
            # Test guide upload
            with open(test_guide_path, 'rb') as f:
                response = client.post('/upload_guide', 
                                     data={'file': (f, 'test_guide.txt')},
                                     content_type='multipart/form-data')
            
            if response.status_code == 302:  # Redirect after successful upload
                print("✅ Guide upload route working")
            else:
                print(f"⚠️  Guide upload returned status: {response.status_code}")
            
            # Test submission upload
            with open(test_submission_path, 'rb') as f:
                response = client.post('/upload_submission', 
                                     data={'file': (f, 'test_submission.txt')},
                                     content_type='multipart/form-data')
            
            if response.status_code == 302:  # Redirect after successful upload
                print("✅ Submission upload route working")
            else:
                print(f"⚠️  Submission upload returned status: {response.status_code}")
        
        # Cleanup
        print("\n8. Cleaning up test files...")
        try:
            os.unlink(test_guide_path)
            os.unlink(test_submission_path)
            print("✅ Test files cleaned up")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")
        
        print("\n🎉 Upload functionality test completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_upload_functionality()
    sys.exit(0 if success else 1)
