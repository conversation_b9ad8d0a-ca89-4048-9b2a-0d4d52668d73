{% extends "base.html" %} {% block title %}Detailed Results{% endblock %} {%
block header %}Detailed Grading Results{% endblock %} {% block header_actions %}
<div class="header-actions">
  <button class="btn btn-primary" onclick="downloadResults()">
    <i class="fas fa-file-excel"></i> Export to Excel
  </button>
  <button class="btn btn-secondary" onclick="window.history.back()">
    <i class="fas fa-arrow-left"></i> Back
  </button>
</div>
{% endblock %} {% block content %}
<div class="detailed-results-container">
  <!-- Result Overview -->
  <section class="result-overview glass-card">
    <div class="overview-header">
      <div class="student-info">
        <h2>{{ result.submission_id }}</h2>
        <span class="grade-badge {{ result.letter_grade|lower }}"
          >{{ result.letter_grade }}</span
        >
      </div>
      <div
        class="score-circle {{ 'excellent' if result.percent_score >= 90 else 'good' if result.percent_score >= 80 else 'fair' if result.percent_score >= 70 else 'poor' }}"
      >
        {{ result.percent_score|round|int }}%
      </div>
    </div>
    <div class="overview-details">
      <div class="detail-item">
        <span class="label">Total Points:</span>
        <span class="value"
          >{{ result.overall_score }} / {{ result.max_possible_score }}</span
        >
      </div>
      <div class="detail-item">
        <span class="label">Graded:</span>
        <span class="value"
          >{{ result.metadata.graded_at|default('N/A', true) }}</span
        >
      </div>
    </div>
  </section>

  <!-- Score Breakdown -->
  <section class="score-breakdown glass-card">
    <h3><i class="fas fa-chart-pie"></i> Score Breakdown</h3>
    <div class="criteria-grid">
      {% for criteria in result.criteria_scores %}
      <div class="criteria-card">
        <div class="criteria-header">
          <h4>
            {{ criteria.description|default('Question ' ~ loop.index, true) }}
          </h4>
          <span class="points"
            >{{ criteria.points_earned }}/{{ criteria.points_possible }}</span
          >
        </div>
        <div class="criteria-details">
          <div class="score-bar">
            <div
              class="score-progress"
              data-progress="{{ (criteria.points_earned / criteria.points_possible * 100)|round }}"
            ></div>
          </div>
          <span class="match-percent"
            >{{ (criteria.similarity * 100)|round }}% Match</span
          >
        </div>
        {% if criteria.feedback %}
        <div class="criteria-feedback">
          <p>{{ criteria.feedback }}</p>
        </div>
        {% endif %}
      </div>
      {% endfor %}
    </div>
  </section>

  <!-- Detailed Feedback -->
  <section class="detailed-feedback">
    <div class="feedback-grid">
      <!-- Strengths -->
      <div class="feedback-card glass-card strengths">
        <h3><i class="fas fa-check-circle"></i> Strengths</h3>
        {% if result.detailed_feedback.strengths %}
        <ul class="feedback-list">
          {% for strength in result.detailed_feedback.strengths %}
          <li>
            <i class="fas fa-check"></i>
            <span>{{ strength }}</span>
          </li>
          {% endfor %}
        </ul>
        {% else %}
        <p class="no-data">No specific strengths identified</p>
        {% endif %}
      </div>

      <!-- Areas to Improve -->
      <div class="feedback-card glass-card improvements">
        <h3><i class="fas fa-exclamation-circle"></i> Areas to Improve</h3>
        {% if result.detailed_feedback.weaknesses %}
        <ul class="feedback-list">
          {% for weakness in result.detailed_feedback.weaknesses %}
          <li>
            <i class="fas fa-exclamation"></i>
            <span>{{ weakness }}</span>
          </li>
          {% endfor %}
        </ul>
        {% else %}
        <p class="no-data">No specific areas for improvement identified</p>
        {% endif %}
      </div>
    </div>
  </section>

  <!-- Answer Details -->
  <section class="answer-details glass-card">
    <h3><i class="fas fa-list-ol"></i> Answer Details</h3>
    <div class="answers-grid">
      {% for answer in result.answers %}
      <div
        class="answer-card {{ 'correct' if answer.is_correct else 'incorrect' }}"
      >
        <div class="answer-header">
          <h4>Question {{ loop.index }}</h4>
          <span class="status-badge">
            {% if answer.is_correct %}
            <i class="fas fa-check"></i> Correct {% else %}
            <i class="fas fa-times"></i> Incorrect {% endif %}
          </span>
        </div>
        <div class="answer-content">
          <div class="answer-item">
            <span class="label">Your Answer:</span>
            <span class="value">{{ answer.given_answer }}</span>
          </div>
          <div class="answer-item">
            <span class="label">Correct Answer:</span>
            <span class="value">{{ answer.correct_answer }}</span>
          </div>
          {% if answer.explanation %}
          <div class="answer-explanation">
            <p>{{ answer.explanation }}</p>
          </div>
          {% endif %}
        </div>
      </div>
      {% endfor %}
    </div>
  </section>
</div>
{% endblock %} {% block scripts %}
<script>
  // Animate progress bars on scroll
  function animateProgressBars() {
    const progressBars = document.querySelectorAll(".score-progress");
    progressBars.forEach((bar) => {
      const progress = bar.dataset.progress;
      bar.style.width = progress + "%";
    });
  }

  // Initialize animations when content is loaded
  document.addEventListener("DOMContentLoaded", () => {
    setTimeout(animateProgressBars, 300);
  });

  // Download functionality
  function downloadResults() {
    showLoading();
    fetch("/download-detailed-results/{{ result.submission_id }}")
      .then((response) => response.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "detailed_results_{{ result.submission_id }}.xlsx";
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        hideLoading();
      })
      .catch((error) => {
        console.error("Error:", error);
        hideLoading();
        alert("Failed to download results");
      });
  }
</script>
{% endblock %} {% block extra_css %}
<style>
  .score-circle-lg {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 2.5rem;
    font-weight: bold;
    color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .list-group-item.border-success {
    border-left: 4px solid var(--bs-success);
  }

  .list-group-item.border-warning {
    border-left: 4px solid var(--bs-warning);
  }

  .list-group-item.border-info {
    border-left: 4px solid var(--bs-info);
  }

  .accordion-button:not(.collapsed) {
    background-color: rgba(13, 110, 253, 0.1);
    color: var(--bs-primary);
  }

  .accordion-button:focus {
    box-shadow: none;
    border-color: rgba(13, 110, 253, 0.25);
  }

  .performance-meter .progress {
    height: 8px;
    border-radius: 4px;
  }

  /* New styles for the detailed results */
  .detailed-results-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
  }

  .header-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
  }

  .header-actions .btn {
    margin-left: 10px;
  }

  .result-overview {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .student-info {
    flex: 1;
  }

  .grade-badge {
    padding: 5px 10px;
    border-radius: 12px;
    font-weight: bold;
    color: white;
  }

  .score-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    font-size: 2rem;
    font-weight: bold;
    color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .overview-details {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
  }

  .detail-item {
    flex: 1;
    margin-right: 10px;
  }

  .detail-item:last-child {
    margin-right: 0;
  }

  .score-breakdown {
    margin-top: 30px;
  }

  .criteria-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }

  .criteria-card {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .criteria-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .points {
    background: #007bff;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: bold;
  }

  .criteria-details {
    margin-top: 10px;
  }

  .score-bar {
    background: #e9ecef;
    border-radius: 4px;
    height: 8px;
    overflow: hidden;
  }

  .score-progress {
    background: #007bff;
    height: 100%;
    transition: width 0.4s ease;
  }

  .match-percent {
    display: block;
    margin-top: 5px;
    font-size: 0.875rem;
    color: #6c757d;
  }

  .detailed-feedback {
    margin-top: 30px;
  }

  .feedback-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .feedback-card {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .feedback-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .feedback-list li {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .feedback-list i {
    color: #28a745;
    margin-right: 10px;
  }

  .no-data {
    color: #6c757d;
    font-style: italic;
  }

  .answer-details {
    margin-top: 30px;
  }

  .answers-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .answer-card {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .answer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .status-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: bold;
    color: white;
  }

  .answer-content {
    margin-top: 10px;
  }

  .answer-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .label {
    color: #6c757d;
  }

  .value {
    font-weight: bold;
  }

  .answer-explanation {
    margin-top: 10px;
    padding: 10px;
    background: #e9ecef;
    border-radius: 4px;
  }
</style>
{% endblock %}
