#!/usr/bin/env python3
"""
Simple test to verify upload functionality works.
"""

import os
import sys
import tempfile
import requests
from pathlib import Path

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

def test_upload_with_requests():
    """Test upload functionality using requests."""
    print("🧪 Testing Upload Functionality with HTTP Requests...")

    try:
        # Test if server is running
        base_url = "http://127.0.0.1:8501"

        print("\n1. Testing server connectivity...")
        try:
            response = requests.get(base_url, timeout=5)
            if response.status_code == 200:
                print("✅ Server is running")
            else:
                print(f"⚠️  Server returned status: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print("❌ Server is not running. Please start the Flask app first.")
            return False
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False

        # Create test files
        print("\n2. Creating test files...")

        # Create a simple text guide
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Test Marking Guide\n\nQuestion 1: What is 2+2?\nAnswer: 4 marks\n\nQuestion 2: Explain photosynthesis.\nAnswer: 10 marks")
            test_guide_path = f.name

        # Create a simple text submission
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Student Submission:\n\nAnswer 1: 2+2 = 4\n\nAnswer 2: Photosynthesis is the process by which plants convert sunlight into energy.")
            test_submission_path = f.name

        print(f"   Test guide: {test_guide_path}")
        print(f"   Test submission: {test_submission_path}")

        # Test guide upload
        print("\n3. Testing guide upload...")
        try:
            with open(test_guide_path, 'rb') as f:
                files = {'file': ('test_guide.txt', f, 'text/plain')}
                response = requests.post(f"{base_url}/upload_guide", files=files, timeout=30)

            if response.status_code in [200, 302]:  # 302 is redirect after successful upload
                print("✅ Guide upload successful")
            else:
                print(f"⚠️  Guide upload returned status: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
        except Exception as e:
            print(f"❌ Guide upload failed: {e}")

        # Test submission upload
        print("\n4. Testing submission upload...")
        try:
            with open(test_submission_path, 'rb') as f:
                files = {'file': ('test_submission.txt', f, 'text/plain')}
                response = requests.post(f"{base_url}/upload_submission", files=files, timeout=30)

            if response.status_code in [200, 302]:  # 302 is redirect after successful upload
                print("✅ Submission upload successful")
            else:
                print(f"⚠️  Submission upload returned status: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
        except Exception as e:
            print(f"❌ Submission upload failed: {e}")

        # Test dashboard access
        print("\n5. Testing dashboard access...")
        try:
            response = requests.get(base_url, timeout=10)
            if response.status_code == 200:
                print("✅ Dashboard accessible")

                # Check if uploads are reflected
                if "Ready" in response.text or "uploaded" in response.text.lower():
                    print("✅ Uploads appear to be reflected in dashboard")
                else:
                    print("⚠️  Uploads may not be reflected in dashboard")
            else:
                print(f"⚠️  Dashboard returned status: {response.status_code}")
        except Exception as e:
            print(f"❌ Dashboard access failed: {e}")

        # Cleanup
        print("\n6. Cleaning up test files...")
        try:
            os.unlink(test_guide_path)
            os.unlink(test_submission_path)
            print("✅ Test files cleaned up")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")

        print("\n🎉 Upload functionality test completed!")
        return True

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("📋 Upload Functionality Test")
    print("=" * 50)
    print("Make sure the Flask app is running on http://127.0.0.1:8501")
    print("You can start it with: python .\\run_app.py")
    print("=" * 50)

    success = test_upload_with_requests()
    sys.exit(0 if success else 1)
