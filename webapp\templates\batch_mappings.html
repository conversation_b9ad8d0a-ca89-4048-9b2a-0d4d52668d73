{% extends "base.html" %} {% block title %}Batch Mapping Results - Exam Grader{%
endblock %} {% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
  <h1 class="mb-0">
    <i class="bi bi-diagram-3 text-success me-2"></i>
    Batch Mapping Results
  </h1>
  <div>
    <a href="{{ url_for('view_results') }}" class="btn btn-success me-2">
      <i class="bi bi-award me-1"></i> View Grading Results
    </a>
    <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
      <i class="bi bi-arrow-left me-1"></i> Back to Home
    </a>
  </div>
</div>

<!-- Summary Statistics -->
<div class="card mb-4 border-0 shadow-sm">
  <div class="card-header bg-gradient-success text-white">
    <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Summary Statistics</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-4 text-center mb-4 mb-md-0">
        <div class="score-container">
          <div class="score-circle mb-3 bg-gradient-success">
            {{ summary.total_mappings }}
          </div>
          <h4 class="mb-2">Total Mappings</h4>
          <div class="score-details">
            <p class="text-muted mb-1">
              <strong>{{ summary.total_submissions }}</strong> submissions
              mapped
            </p>
          </div>
        </div>
      </div>
      <div class="col-md-8">
        <div class="row">
          <div class="col-md-6 mb-3">
            <div class="stat-card">
              <div class="stat-icon bg-success-light">
                <i class="bi bi-diagram-3 text-success"></i>
              </div>
              <div class="stat-content">
                <h6 class="stat-title">Average Mappings per Submission</h6>
                <div class="stat-value">
                  {{ summary.average_mappings|round(1) }}
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <div class="stat-card">
              <div class="stat-icon bg-info-light">
                <i class="bi bi-file-earmark-text text-info"></i>
              </div>
              <div class="stat-content">
                <h6 class="stat-title">Total Submissions</h6>
                <div class="stat-value">{{ summary.total_submissions }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Individual Results Table -->
<div class="card mb-4 border-0 shadow-sm">
  <div
    class="card-header bg-gradient-success text-white d-flex justify-content-between align-items-center"
  >
    <h5 class="mb-0"><i class="bi bi-table me-2"></i>Individual Mappings</h5>
    <div class="d-flex align-items-center">
      <span class="text-white me-2">Page:</span>
      <div
        class="btn-group pagination-controls"
        role="group"
        id="pagination-buttons"
      >
        <!-- Pagination buttons will be generated by JavaScript -->
      </div>
    </div>
  </div>
  <div class="card-body p-0">
    <div class="table-responsive">
      <table class="table table-hover mb-0">
        <thead class="table-light">
          <tr>
            <th>Submission ID</th>
            <th class="text-center">Mappings</th>
            <th class="text-center">Actions</th>
          </tr>
        </thead>
        <tbody id="results-table-body">
          <!-- Table rows will be populated by JavaScript -->
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer bg-light">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <span class="text-muted"
          >Showing <span id="page-start">1</span>-<span id="page-end">10</span>
          of <span id="total-results">{{ results|length }}</span> results</span
        >
      </div>
      <nav aria-label="Results pagination">
        <ul class="pagination pagination-sm mb-0">
          <li class="page-item" id="prev-page">
            <a class="page-link" href="#" aria-label="Previous">
              <span aria-hidden="true">&laquo;</span>
            </a>
          </li>
          <li class="page-item" id="next-page">
            <a class="page-link" href="#" aria-label="Next">
              <span aria-hidden="true">&raquo;</span>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</div>

<!-- Result Detail Modal -->
<div
  class="modal fade"
  id="resultDetailModal"
  tabindex="-1"
  aria-labelledby="resultDetailModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="resultDetailModalLabel">Mapping Details</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h6 class="mb-0" id="modal-submission-id"></h6>
          <div>
            <span class="badge bg-success" id="modal-mappings-count"></span>
          </div>
        </div>

        <h6 class="mb-2">Mapped Questions</h6>
        <div class="table-responsive">
          <table class="table table-sm table-bordered mb-3">
            <thead class="table-secondary">
              <tr>
                <th style="width: 45%">Guide Question</th>
                <th style="width: 45%">Student Answer</th>
                <th class="text-center" style="width: 10%">Match %</th>
              </tr>
            </thead>
            <tbody id="modal-mappings">
              <!-- Mappings will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Close
        </button>
        <button
          type="button"
          class="btn btn-success"
          id="view-full-mapping-btn"
        >
          <i class="bi bi-file-earmark-text me-1"></i> View Full Mapping
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_css %}
<style>
  .score-pill {
    display: inline-block;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: bold;
  }

  .grade-item {
    margin-right: 1rem;
    margin-bottom: 0.5rem;
  }

  .score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
    font-size: 2rem;
    font-weight: bold;
  }

  .bg-gradient-success {
    background: linear-gradient(135deg, #28a745, #20c997);
  }

  .bg-gradient-primary {
    background: linear-gradient(135deg, #007bff, #6610f2);
  }

  .bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
  }

  .bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
  }

  .stat-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #f8f9fa;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.5rem;
  }

  .bg-success-light {
    background-color: rgba(25, 135, 84, 0.15);
  }

  .bg-info-light {
    background-color: rgba(13, 202, 240, 0.15);
  }

  .bg-danger-light {
    background-color: rgba(220, 53, 69, 0.15);
  }

  .stat-title {
    color: #6c757d;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
  }

  .stat-value {
    font-size: 1.25rem;
    font-weight: bold;
  }

  /* Pagination styles */
  .pagination-controls .btn {
    margin: 0 2px;
    min-width: 32px;
  }

  .pagination-controls .btn.active {
    background-color: #28a745;
    color: white;
    font-weight: bold;
  }
</style>
{% endblock %} {% block extra_js %}
<script>
  // Store all results data
  const allResults = {{ results|tojson }};

  // Pagination settings
  const resultsPerPage = 10;
  let currentPage = 1;
  const totalPages = Math.ceil(allResults.length / resultsPerPage);

  // Initialize pagination
  document.addEventListener('DOMContentLoaded', function() {
    // Generate pagination buttons
    generatePaginationButtons();

    // Display first page
    displayResultsPage(1);

    // Add event listeners for pagination controls
    document.getElementById('prev-page').addEventListener('click', function(e) {
      e.preventDefault();
      if (currentPage > 1) {
        displayResultsPage(currentPage - 1);
      }
    });

    document.getElementById('next-page').addEventListener('click', function(e) {
      e.preventDefault();
      if (currentPage < totalPages) {
        displayResultsPage(currentPage + 1);
      }
    });
  });

  // Generate pagination buttons
  function generatePaginationButtons() {
    const paginationContainer = document.getElementById('pagination-buttons');
    paginationContainer.innerHTML = '';

    // Only show page buttons if we have more than one page
    if (totalPages > 1) {
      // Determine which pages to show
      let pagesToShow = [];

      if (totalPages <= 5) {
        // Show all pages if 5 or fewer
        for (let i = 1; i <= totalPages; i++) {
          pagesToShow.push(i);
        }
      } else {
        // Always show first page
        pagesToShow.push(1);

        // Show current page and neighbors
        if (currentPage > 2) {
          pagesToShow.push('...');
        }

        // Show current page and neighbors
        if (currentPage > 1 && currentPage < totalPages) {
          if (currentPage > 2) {
            pagesToShow.push(currentPage - 1);
          }
          pagesToShow.push(currentPage);
          if (currentPage < totalPages - 1) {
            pagesToShow.push(currentPage + 1);
          }
        }

        // Add ellipsis if needed
        if (currentPage < totalPages - 1) {
          pagesToShow.push('...');
        }

        // Always show last page
        pagesToShow.push(totalPages);
      }

      // Create buttons
      pagesToShow.forEach(page => {
        const button = document.createElement('button');
        button.type = 'button';

        if (page === '...') {
          button.className = 'btn btn-sm btn-light disabled';
          button.textContent = '...';
        } else {
          button.className = page === currentPage ? 'btn btn-sm btn-light active' : 'btn btn-sm btn-light';
          button.textContent = page;
          button.addEventListener('click', function() {
            displayResultsPage(page);
          });
        }

        paginationContainer.appendChild(button);
      });
    }

    // Update prev/next buttons state
    document.getElementById('prev-page').classList.toggle('disabled', currentPage === 1);
    document.getElementById('next-page').classList.toggle('disabled', currentPage === totalPages);
  }

  // Display results for a specific page
  function displayResultsPage(page) {
    currentPage = page;

    // Calculate start and end indices
    const startIndex = (page - 1) * resultsPerPage;
    const endIndex = Math.min(startIndex + resultsPerPage, allResults.length);

    // Get results for this page
    const pageResults = allResults.slice(startIndex, endIndex);

    // Update the table body
    const tableBody = document.querySelector('#results-table-body');
    tableBody.innerHTML = '';

    // Add rows for each result
    pageResults.forEach((result, index) => {
      const actualIndex = startIndex + index;

      // Create the main row
      const row = document.createElement('tr');

      // Submission ID cell
      const idCell = document.createElement('td');
      idCell.innerHTML = `
        <div class="d-flex align-items-center">
          <i class="bi bi-file-earmark-text me-2 text-secondary"></i>
          <div>
            <div class="fw-bold">${result.submission_id}</div>
            <div class="small text-muted">Mapped: ${result.metadata?.mapping_time || 'N/A'}</div>
          </div>
        </div>
      `;

      // Mappings count cell
      const mappingsCell = document.createElement('td');
      mappingsCell.className = 'text-center';
      const mappingsCount = result.mappings ? result.mappings.length : 0;
      mappingsCell.innerHTML = `
        <span class="badge bg-success fs-6 px-3 py-2">${mappingsCount}</span>
      `;

      // Actions cell
      const actionsCell = document.createElement('td');
      actionsCell.className = 'text-center';
      actionsCell.innerHTML = `
        <button class="btn btn-sm btn-outline-success view-details-btn" data-index="${actualIndex}">
          <i class="bi bi-eye me-1"></i> View Details
        </button>
      `;

      // Add cells to row
      row.appendChild(idCell);
      row.appendChild(mappingsCell);
      row.appendChild(actionsCell);

      // Add row to table
      tableBody.appendChild(row);
    });

    // Add event listeners to view details buttons
    document.querySelectorAll('.view-details-btn').forEach(button => {
      button.addEventListener('click', function() {
        const index = parseInt(this.getAttribute('data-index'));
        showResultDetails(index);
      });
    });

    // Update pagination info
    document.getElementById('page-start').textContent = startIndex + 1;
    document.getElementById('page-end').textContent = endIndex;
    document.getElementById('total-results').textContent = allResults.length;

    // Update pagination buttons
    generatePaginationButtons();
  }

  // Show result details in a modal
  function showResultDetails(index) {
    const result = allResults[index];

    // Set modal title and content
    document.getElementById('modal-submission-id').textContent = result.submission_id;
    document.getElementById('modal-mappings-count').textContent = `${result.mappings ? result.mappings.length : 0} Mappings`;

    // Populate mappings
    const mappingsTable = document.getElementById('modal-mappings');
    mappingsTable.innerHTML = '';

    if (result.mappings && result.mappings.length > 0) {
      result.mappings.forEach(mapping => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${mapping.guide_text ? mapping.guide_text.substring(0, 100) : 'N/A'}${mapping.guide_text && mapping.guide_text.length > 100 ? '...' : ''}</td>
          <td>${mapping.submission_text ? mapping.submission_text.substring(0, 100) : 'N/A'}${mapping.submission_text && mapping.submission_text.length > 100 ? '...' : ''}</td>
          <td class="text-center">${Math.round(mapping.match_score * 100)}%</td>
        `;
        mappingsTable.appendChild(row);
      });
    } else {
      mappingsTable.innerHTML = '<tr><td colspan="3" class="text-center text-muted">No mappings available</td></tr>';
    }

    // Set up view full mapping button
    document.getElementById('view-full-mapping-btn').onclick = function() {
      viewFullMapping(index);
    };

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('resultDetailModal'));
    modal.show();
  }

  function viewFullMapping(index) {
    // Store the selected mapping in session and redirect to individual mapping page
    fetch("/set_selected_mapping", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ index: index }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          window.location.href = "/view_mapping";
        }
      });
  }
</script>
{% endblock %}
