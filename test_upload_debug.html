<!DOCTYPE html>
<html>
<head>
    <title>Upload Debug Test</title>
    <style>
        .upload-dropzone {
            border: 2px dashed #ccc;
            padding: 2rem;
            text-align: center;
            background: #f9f9f9;
            cursor: pointer;
            margin: 20px;
        }
        .upload-dropzone:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .upload-dropzone.dragover {
            border-color: #007bff;
            background: #bbdefb;
        }
    </style>
</head>
<body>
    <h1>Upload Debug Test</h1>
    
    <form action="http://127.0.0.1:8501/upload_guide" method="post" enctype="multipart/form-data" id="guideForm">
        <div class="upload-dropzone" id="guideDropzone">
            <input type="file" id="guideFile" name="file" accept=".docx,.txt" style="display: none;">
            <h3>Drop Guide Here or Click to Browse</h3>
            <p>Supports: .docx, .txt files</p>
        </div>
    </form>
    
    <form action="http://127.0.0.1:8501/upload_submission" method="post" enctype="multipart/form-data" id="submissionForm">
        <div class="upload-dropzone" id="submissionDropzone">
            <input type="file" id="submissionFile" name="file" accept=".docx,.txt,.pdf,.jpg,.jpeg,.png,.tiff,.bmp,.gif" multiple style="display: none;">
            <h3>Drop Submissions Here or Click to Browse</h3>
            <p>Supports: PDF, Word, Text, Images • Multiple files allowed</p>
        </div>
    </form>
    
    <div id="debug-log" style="margin: 20px; padding: 10px; background: #f0f0f0; border-radius: 5px;">
        <h4>Debug Log:</h4>
        <div id="log-content"></div>
    </div>

    <script>
        function log(message) {
            const logContent = document.getElementById('log-content');
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            console.log(message);
        }

        document.addEventListener('DOMContentLoaded', function() {
            log('DOM Content Loaded');
            
            // Helper function to validate file
            function validateFile(file, allowedTypes, maxSize = 20 * 1024 * 1024) {
                const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
                const isValidType = allowedTypes.includes(fileExtension);
                const isValidSize = file.size <= maxSize;
                
                return {
                    valid: isValidType && isValidSize,
                    error: !isValidType ? `File type ${fileExtension} not supported` : 
                           !isValidSize ? `File size too large (max 20MB)` : null
                };
            }

            // Setup guide upload
            const guideDropzone = document.getElementById('guideDropzone');
            const guideFileInput = document.getElementById('guideFile');
            const guideForm = document.getElementById('guideForm');
            
            if (guideDropzone && guideFileInput) {
                log('Setting up guide upload handlers');
                const allowedGuideTypes = ['.docx', '.txt'];
                
                // Click to upload
                guideDropzone.addEventListener('click', () => {
                    log('Guide dropzone clicked');
                    guideFileInput.click();
                });
                
                // File input change
                guideFileInput.addEventListener('change', function() {
                    log(`Guide file input changed, files: ${this.files.length}`);
                    if (this.files.length > 0) {
                        const file = this.files[0];
                        log(`Selected file: ${file.name}, size: ${file.size}, type: ${file.type}`);
                        
                        const validation = validateFile(file, allowedGuideTypes);
                        log(`Validation result: ${validation.valid}, error: ${validation.error}`);
                        
                        if (validation.valid) {
                            log('Submitting guide form');
                            guideForm.submit();
                        } else {
                            log(`Validation failed: ${validation.error}`);
                            alert(validation.error);
                            this.value = '';
                        }
                    }
                });
                
                // Drag and drop events
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    guideDropzone.addEventListener(eventName, function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        log(`Guide drag event: ${eventName}`);
                    }, false);
                });
                
                ['dragenter', 'dragover'].forEach(eventName => {
                    guideDropzone.addEventListener(eventName, () => {
                        guideDropzone.classList.add('dragover');
                    }, false);
                });
                
                ['dragleave', 'drop'].forEach(eventName => {
                    guideDropzone.addEventListener(eventName, () => {
                        guideDropzone.classList.remove('dragover');
                    }, false);
                });
                
                guideDropzone.addEventListener('drop', function(e) {
                    log('Guide drop event');
                    const files = e.dataTransfer.files;
                    log(`Dropped files: ${files.length}`);
                    
                    if (files.length > 0) {
                        const file = files[0];
                        log(`Dropped file: ${file.name}, size: ${file.size}`);
                        
                        const validation = validateFile(file, allowedGuideTypes);
                        if (validation.valid) {
                            guideFileInput.files = files;
                            log('Submitting guide form via drop');
                            guideForm.submit();
                        } else {
                            log(`Drop validation failed: ${validation.error}`);
                            alert(validation.error);
                        }
                    }
                });
            } else {
                log('Guide elements not found!');
            }

            // Setup submission upload (similar to guide)
            const submissionDropzone = document.getElementById('submissionDropzone');
            const submissionFileInput = document.getElementById('submissionFile');
            const submissionForm = document.getElementById('submissionForm');
            
            if (submissionDropzone && submissionFileInput) {
                log('Setting up submission upload handlers');
                const allowedSubmissionTypes = ['.docx', '.txt', '.pdf', '.jpg', '.jpeg', '.png', '.tiff', '.bmp', '.gif'];
                
                submissionDropzone.addEventListener('click', () => {
                    log('Submission dropzone clicked');
                    submissionFileInput.click();
                });
                
                submissionFileInput.addEventListener('change', function() {
                    log(`Submission file input changed, files: ${this.files.length}`);
                    if (this.files.length > 0) {
                        const files = Array.from(this.files);
                        let allValid = true;
                        let errorMessage = '';
                        
                        for (let file of files) {
                            const validation = validateFile(file, allowedSubmissionTypes);
                            if (!validation.valid) {
                                allValid = false;
                                errorMessage = validation.error;
                                break;
                            }
                        }
                        
                        if (allValid) {
                            log('Submitting submission form');
                            submissionForm.submit();
                        } else {
                            log(`Submission validation failed: ${errorMessage}`);
                            alert(errorMessage);
                            this.value = '';
                        }
                    }
                });
            } else {
                log('Submission elements not found!');
            }
            
            log('Upload handlers setup complete');
        });
    </script>
</body>
</html>
