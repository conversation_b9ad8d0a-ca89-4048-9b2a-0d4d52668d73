{% extends "base.html" %}

{% block title %}Exam Grader - Dashboard{% endblock %}

{% block header %}Dashboard{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="text-center mb-5">
    <h1 class="display-4 fw-bold mb-3" style="background: var(--primary-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Exam Grader</h1>
    <p class="lead mb-4" style="color: var(--gray-600); max-width: 600px; margin: 0 auto;">AI-powered, fast, and reliable grading for your exams. Upload your marking guide and student submissions to get started!</p>
</div>

<!-- Progress Indicator -->
<div class="d-flex justify-content-center mb-5">
    <div class="progress" style="width: 400px; height: 8px;">
        <div class="progress-bar" role="progressbar" style="width: {{ progress_value }}%;" aria-valuenow="{{ progress_value }}" aria-valuemin="0" aria-valuemax="100"></div>
    </div>
</div>

<!-- File Upload Section -->
<div class="row g-4 mb-5">
    <!-- Marking Guide Upload -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="bi bi-journal-text" style="color: var(--primary);"></i>Upload Marking Guide</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('upload_guide') }}" method="post" enctype="multipart/form-data" id="guideForm">
                    <div class="file-upload-area mb-3">
                        <input type="file" class="d-none" id="guideFile" name="file" accept=".docx,.txt">
                        <div class="file-upload-icon">
                            <i class="bi bi-cloud-arrow-up"></i>
                        </div>
                        <h5>Drop your marking guide here</h5>
                        <p class="text-muted">or click to browse files</p>
                        <p class="text-muted small">Supported formats: .docx, .txt</p>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="guideButton" data-loading-text="Uploading Guide...">
                            <i class="bi bi-upload"></i> Upload Guide
                        </button>

                        {% if session.get('guide_uploaded') %}
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('view_guide') }}" class="btn btn-outline-primary flex-fill">
                                <i class="bi bi-eye"></i> View
                            </a>
                            <form action="{{ url_for('clear_guide') }}" method="post" class="flex-fill">
                                <button type="submit" class="btn btn-outline-danger w-100" data-confirm="Clear guide?">
                                    <i class="bi bi-trash"></i> Clear
                                </button>
                            </form>
                        </div>
                        {% endif %}
                    </div>
                </form>

                {% if session.get('guide_uploaded') %}
                <div class="alert alert-success mt-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <strong>Guide loaded!</strong>
                        </div>
                        <div class="text-end">
                            <div>Raw text extracted</div>
                            <small>{{ session.get('guide_content', '')|length }} characters</small>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Student Submission Upload -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="bi bi-file-earmark-text" style="color: var(--info);"></i>Upload Student Submission</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('upload_submission') }}" method="post" enctype="multipart/form-data" id="submissionForm">
                    <div class="file-upload-area mb-3">
                        <input type="file" class="d-none" id="submissionFile" name="file" accept=".docx,.txt,.pdf,.jpg,.jpeg,.png,.tiff,.bmp,.gif" multiple>
                        <div class="file-upload-icon">
                            <i class="bi bi-cloud-arrow-up"></i>
                        </div>
                        <h5>Drop your student submissions here</h5>
                        <p class="text-muted">or click to browse files</p>
                        <p class="text-muted small">Supported formats: .docx, .txt, .pdf, images</p>
                        <p class="text-primary small"><i class="bi bi-info-circle"></i> You can select multiple files</p>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-info" id="submissionButton"
                                {{ 'disabled' if not session.get('guide_uploaded') }}
                                data-loading-text="Uploading Submission...">
                            <i class="bi bi-upload"></i> Upload Submissions
                        </button>

                        {% if not session.get('guide_uploaded') %}
                        <div class="alert alert-warning mb-0">
                            <i class="bi bi-exclamation-triangle"></i>
                            Please upload a marking guide first
                        </div>
                        {% endif %}

                        {% if session.get('submissions') or session.get('last_submission') %}
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('view_submission') }}" class="btn btn-outline-info flex-fill">
                                <i class="bi bi-eye"></i> View
                            </a>
                            <form action="{{ url_for('clear_submission') }}" method="post" class="flex-fill">
                                <button type="submit" class="btn btn-outline-danger w-100" data-confirm="Clear submissions?">
                                    <i class="bi bi-trash"></i> Clear
                                </button>
                            </form>
                        </div>
                        {% endif %}
                    </div>
                </form>

                {% if session.get('submissions') %}
                <div class="alert alert-success mt-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <strong>{{ session.get('submissions')|length }} Submission(s) loaded!</strong>
                        </div>
                        <div class="text-end">
                            <a href="{{ url_for('view_submission') }}" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-eye me-1"></i> View All
                            </a>
                        </div>
                    </div>
                </div>
                <div class="submission-list mt-3">
                    {% for submission in session.get('submissions', []) %}
                    <div class="submission-item p-2 border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="bi bi-file-earmark-text me-2"></i>
                                <span class="submission-filename">{{ submission.filename }}</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <small class="text-muted me-2">{{ "%.1f"|format(submission.raw_text|length / 1024) }} KB</small>
                                <form action="{{ url_for('remove_submission', filename=submission.filename) }}" method="post" class="d-inline">
                                    <button type="submit" class="btn btn-sm btn-outline-danger"
                                            data-confirm="Are you sure you want to remove this submission?">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                {% if session.get('last_submission') and not session.get('submissions') %}
                <div class="alert alert-success mt-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <strong>Submission loaded!</strong>
                        </div>
                        <div class="text-end">
                            <small>{{ session.get('last_submission', {}).get('filename', '') }}</small>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- AI Processing Section -->
<div class="row g-4 mb-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-robot" style="color: var(--primary);"></i>AI Processing & Grading</h5>
            </div>
            <div class="card-body">
                <p class="mb-4">Process submissions with AI to map answers to marking criteria and generate grades automatically.</p>

                <!-- Single Submission Processing -->
                <div class="row g-3 mb-4">
                    <div class="col-md-6">
                        <form action="{{ url_for('grade_submission') }}" method="post" class="d-grid gap-3">
                            <div class="mb-3">
                                <label for="num_questions" class="form-label">Number of questions to answer:</label>
                                <input type="number" class="form-control" id="num_questions" name="num_questions" min="1" value="1">
                                <div class="form-text">How many questions should students answer from the guide.</div>
                            </div>

                            {% if session.get('submissions') and session.get('submissions')|length > 1 %}
                            <div class="mb-3">
                                <label for="submission_id" class="form-label">Select Submission</label>
                                <select class="form-select" id="submission_id" name="submission_id">
                                    {% for sub in session.get('submissions', []) %}
                                    <option value="{{ sub.filename }}">{{ sub.filename }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            {% endif %}

                            <button type="submit" class="btn btn-primary btn-lg"
                                    {{ 'disabled' if not session.get('guide_uploaded') or (not session.get('last_submission') and not session.get('submissions')) }}
                                    data-loading-text="Processing & Grading...">
                                <i class="bi bi-robot"></i> Process & Grade
                            </button>
                        </form>
                    </div>

                    <div class="col-md-6">
                        <div class="d-grid gap-3">
                            <!-- Batch Processing -->
                            <form action="{{ url_for('batch_grade') }}" method="post">
                                <input type="hidden" name="num_questions" value="{{ session.get('num_questions', 1) }}">
                                <button type="submit" class="btn btn-success btn-lg w-100"
                                        {{ 'disabled' if not session.get('guide_uploaded') or not session.get('submissions') or session.get('submissions')|length < 2 }}
                                        data-loading-text="Batch Processing...">
                                    <i class="bi bi-people"></i> Process All Submissions
                                </button>
                            </form>

                            <!-- Quick Actions -->
                            <div class="row g-2">
                                {% if session.get('last_grading_result') or session.get('last_mapping_result') %}
                                <div class="col-6">
                                    <a href="{{ url_for('view_results') }}" class="btn btn-outline-primary w-100">
                                        <i class="bi bi-eye"></i> View Results
                                    </a>
                                </div>
                                {% endif %}
                                {% if session.get('batch_results') %}
                                <div class="col-6">
                                    <a href="{{ url_for('view_batch_results') }}" class="btn btn-outline-success w-100">
                                        <i class="bi bi-layers"></i> Batch Results
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                {% if not session.get('guide_uploaded') or (not session.get('last_submission') and not session.get('submissions')) %}
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    Please upload both a marking guide and at least one student submission first
                </div>
                {% endif %}

                {% if session.get('last_grading_result') %}
                <div class="alert alert-success">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-check-circle-fill"></i>
                            <strong>Processing complete!</strong>
                        </div>
                        <div>
                            <span class="badge badge-success">Score: {{ session.get('last_score', 0) }}%</span>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- System Status & Management -->
<div class="row g-4">
    <div class="col-md-8">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="bi bi-info-circle" style="color: var(--secondary);"></i>System Status</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="status-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">OCR Service</h6>
                                    <small style="color: var(--gray-500);">Image processing</small>
                                </div>
                                <div>
                                    {% if ocr_status %}
                                    <span class="badge badge-success"><i class="bi bi-check-circle"></i></span>
                                    {% else %}
                                    <span class="badge badge-danger"><i class="bi bi-x-circle"></i></span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="status-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">AI Engine</h6>
                                    <small style="color: var(--gray-500);">DeepSeek LLM</small>
                                </div>
                                <div>
                                    {% if llm_status %}
                                    <span class="badge badge-success"><i class="bi bi-check-circle"></i></span>
                                    {% else %}
                                    <span class="badge badge-danger"><i class="bi bi-x-circle"></i></span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="status-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">Storage</h6>
                                    <small style="color: var(--gray-500);">Cache usage</small>
                                </div>
                                <div>
                                    {% if storage_stats %}
                                    <span class="badge badge-info">{{ "%.1f"|format(storage_stats.total_size_mb) }}/{{ storage_stats.max_size_mb|int }}MB</span>
                                    {% else %}
                                    <span class="badge badge-secondary"><i class="bi bi-question-circle"></i></span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="bi bi-gear" style="color: var(--warning);"></i>Management</h5>
            </div>
            <div class="card-body d-flex flex-column justify-content-center">
                <p class="mb-3 text-center">Clear all data and start fresh</p>
                <form action="{{ url_for('clear_all') }}" method="post" id="clearAllForm" class="d-grid">
                    <button type="submit" class="btn btn-warning" id="clearAllButton"
                            data-confirm="Clear all data and cache files? This cannot be undone."
                            data-loading-text="Clearing...">
                        <i class="bi bi-trash"></i> Clear All Data
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Loaders are now handled by the loader.js component -->
{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard specific styles */
    .submission-list {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid var(--gray-200);
        border-radius: var(--border-radius);
        background: var(--gray-50);
    }

    .submission-item {
        transition: var(--transition);
        padding: 0.75rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .submission-item:hover {
        background-color: rgba(79, 70, 229, 0.05);
    }

    .submission-item:last-child {
        border-bottom: none !important;
    }

    .submission-filename {
        font-weight: 500;
        color: var(--gray-700);
    }

    /* Enhanced card hover effects */
    .card:hover .card-header h5 i {
        transform: scale(1.1);
        transition: var(--transition);
    }

    /* Progress indicator styling */
    .progress {
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* Welcome section gradient text fallback */
    .display-4 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        color: transparent;
    }

    /* Status card enhancements */
    .status-card h6 {
        color: var(--gray-700);
        font-weight: 600;
    }

    /* Button group improvements */
    .btn-lg {
        padding: 1rem 2rem;
        font-size: 1rem;
        font-weight: 600;
    }

    /* Processing section styling */
    .card .row .col-md-6:first-child {
        border-right: 1px solid var(--gray-200);
    }

    @media (max-width: 768px) {
        .card .row .col-md-6:first-child {
            border-right: none;
            border-bottom: 1px solid var(--gray-200);
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Make file upload areas work
    document.addEventListener('DOMContentLoaded', function() {
        // File upload handlers
        document.getElementById('guideFile').addEventListener('change', function() {
            if (this.files.length > 0) {
                // Show loader for guide upload
                const guideButton = document.getElementById('guideButton');
                toggleButtonLoader(guideButton, true);

                // Create a simple loader
                const loader = new Loader({
                    type: 'spinner',
                    text: 'Uploading Marking Guide...',
                    subText: 'Please wait while we process your file'
                });
                loader.show();

                // Submit the form
                document.getElementById('guideForm').submit();
            }
        });

        document.getElementById('submissionFile').addEventListener('change', function() {
            if (this.files.length > 0) {
                // Show loader for submission upload
                const submissionButton = document.getElementById('submissionButton');
                toggleButtonLoader(submissionButton, true);

                // Create a simple loader
                const loader = new Loader({
                    type: 'pulse',
                    text: 'Uploading Submission...',
                    subText: 'This may take a moment if OCR is needed'
                });
                loader.show();

                // Submit the form
                document.getElementById('submissionForm').submit();
            }
        });

        // Make file upload areas clickable
        document.querySelectorAll('.file-upload-area').forEach(area => {
            area.addEventListener('click', function() {
                const fileInput = this.querySelector('input[type="file"]');
                if (fileInput) {
                    fileInput.click();
                }
            });
        });

        // Add confirmation for buttons
        document.querySelectorAll('[data-confirm]').forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm(this.getAttribute('data-confirm'))) {
                    e.preventDefault();
                }
            });
        });

        // Add loader for processing & grading (combined functionality)
        document.querySelector('form[action*="grade_submission"]')?.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show button loader
            const gradeButton = this.querySelector('button[type="submit"]');
            toggleButtonLoader(gradeButton, true);

            // Create a loader with appropriate messaging
            const loader = new Loader({
                type: 'dots',
                text: 'AI Processing & Grading...',
                subText: 'Mapping answers and calculating scores'
            });
            loader.show();

            // Create a progress tracker
            fetch('/api/progress', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation_type: 'llm',
                    task_name: 'Processing & Grading',
                    total_steps: 100
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.id) {
                    // Start tracking progress
                    progressTracker.startTracking(data.id, 'llm');
                }
                // Submit the form
                this.submit();
            })
            .catch(error => {
                console.error('Error creating progress tracker:', error);
                // Submit the form anyway
                this.submit();
            });
        });

        // Add loader for batch grading
        document.querySelector('form[action*="batch_grade"]')?.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show button loader
            const batchButton = this.querySelector('button[type="submit"]');
            toggleButtonLoader(batchButton, true);

            // Create a loader with appropriate messaging
            const loader = new Loader({
                type: 'dots',
                text: 'Batch Grading in Progress...',
                subText: 'This may take several minutes for multiple submissions'
            });
            loader.show();

            // Create a progress tracker
            fetch('/api/progress', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation_type: 'batch_grading',
                    task_name: 'Batch Grading',
                    total_steps: 100
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.id) {
                    // Start tracking progress
                    progressTracker.startTracking(data.id, 'batch_grading');

                    // Submit the form
                    this.submit();
                } else {
                    // If we couldn't create a tracker, still submit the form
                    this.submit();
                }
            })
            .catch(error => {
                console.error('Error creating progress tracker:', error);
                // Still submit the form even if tracker creation fails
                this.submit();
            });
        });





        // Add loader for clear all button
        document.getElementById('clearAllForm')?.addEventListener('submit', function(e) {
            // This will only run after the user has confirmed
            const clearButton = document.getElementById('clearAllButton');
            toggleButtonLoader(clearButton, true);

            // Show a loader
            const loader = new Loader({
                type: 'spinner',
                text: 'Clearing All Data...',
                subText: 'Removing all files and cache'
            });
            loader.show();
        });
    });
</script>
{% endblock %}