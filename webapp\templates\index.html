{% extends "base.html" %}

{% block title %}Exam Grader - Dashboard{% endblock %}

{% block header %}Dashboard{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="text-center mb-5">
    <h1 class="display-4 fw-bold mb-3" style="background: var(--primary-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Exam Grader</h1>
    <p class="lead mb-4" style="color: var(--gray-600); max-width: 600px; margin: 0 auto;">AI-powered, fast, and reliable grading for your exams. Upload your marking guide and student submissions to get started!</p>
</div>

<!-- Progress Indicator -->
<div class="d-flex justify-content-center mb-5">
    <div class="progress" style="width: 400px; height: 8px;">
        <div class="progress-bar" role="progressbar" style="width: {{ progress_value }}%;" aria-valuenow="{{ progress_value }}" aria-valuemin="0" aria-valuemax="100"></div>
    </div>
</div>

<!-- Dashboard Cards Grid -->
<div class="dashboard-grid">
    <!-- Stats Cards - Two Rows, Three Columns Each -->
    <!-- First Row -->
    <div class="row g-3 mb-4">
        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-journal-text"></i>
                </div>
                <div class="stat-content">
                    <h3>Guide Status</h3>
                    {% if session.get('guide_uploaded') %}
                    <div class="stat-value stat-success">Ready</div>
                    <div class="stat-label">{{ "%.1f"|format(session.get('guide_content', '')|length / 1000) }}k chars</div>
                    {% else %}
                    <div class="stat-value stat-muted">Not uploaded</div>
                    <div class="stat-label">Upload required</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-files"></i>
                </div>
                <div class="stat-content">
                    <h3>Submissions</h3>
                    <div class="stat-value">{{ session.get('submissions', [])|length or (1 if session.get('last_submission') else 0) }}</div>
                    <div class="stat-label">Files uploaded</div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-award"></i>
                </div>
                <div class="stat-content">
                    <h3>Last Score</h3>
                    {% if session.get('last_score') %}
                    <div class="stat-value stat-primary">{{ session.get('last_score', 0) }}%</div>
                    <div class="stat-label">Recent grade</div>
                    {% else %}
                    <div class="stat-value stat-muted">--</div>
                    <div class="stat-label">No grades yet</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row -->
    <div class="row g-3 mb-4">
        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-robot"></i>
                </div>
                <div class="stat-content">
                    <h3>AI Status</h3>
                    {% if llm_status %}
                    <div class="stat-value stat-success">Online</div>
                    <div class="stat-label">Ready to grade</div>
                    {% else %}
                    <div class="stat-value stat-danger">Offline</div>
                    <div class="stat-label">Service unavailable</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-clock-history"></i>
                </div>
                <div class="stat-content">
                    <h3>Cache Usage</h3>
                    {% if storage_stats %}
                    <div class="stat-value">{{ "%.0f"|format(storage_stats.total_size_mb) }}MB</div>
                    <div class="stat-label">Storage used</div>
                    {% else %}
                    <div class="stat-value stat-muted">0MB</div>
                    <div class="stat-label">Storage used</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-activity"></i>
                </div>
                <div class="stat-content">
                    <h3>System Status</h3>
                    {% if session.get('last_grading_result') or session.get('batch_results') %}
                    <div class="stat-value stat-success">Active</div>
                    <div class="stat-label">Results available</div>
                    {% else %}
                    <div class="stat-value stat-muted">Idle</div>
                    <div class="stat-label">No recent activity</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row g-3 mb-4">
        <!-- Upload Section -->
        <div class="col-lg-5">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-cloud-upload"></i> Upload Files</h4>
                    <p class="card-subtitle">Upload guide and submissions</p>
                </div>
                <div class="card-content">
                    <!-- Guide Upload -->
                    <div class="upload-section-compact mb-3">
                        <div class="upload-header-compact">
                            <h5>Marking Guide</h5>
                            {% if session.get('guide_uploaded') %}
                            <span class="status-badge success">
                                <i class="bi bi-check-circle"></i> Ready
                            </span>
                            {% else %}
                            <span class="status-badge pending">
                                <i class="bi bi-clock"></i> Required
                            </span>
                            {% endif %}
                        </div>
                        <form action="{{ url_for('upload_guide') }}" method="post" enctype="multipart/form-data" id="guideForm">
                            <div class="upload-zone-compact" onclick="document.getElementById('guideFile').click()">
                                <input type="file" id="guideFile" name="file" accept=".docx,.txt" style="display: none;">
                                <i class="bi bi-file-earmark-text upload-icon-compact"></i>
                                <span>Click to upload guide</span>
                            </div>
                            {% if session.get('guide_uploaded') %}
                            <div class="action-buttons-compact">
                                <a href="{{ url_for('view_guide') }}" class="btn-compact btn-secondary">
                                    <i class="bi bi-eye"></i> View
                                </a>
                                <form action="{{ url_for('clear_guide') }}" method="post" style="display: inline;">
                                    <button type="submit" class="btn-compact btn-danger" data-confirm="Clear guide?">
                                        <i class="bi bi-trash"></i> Clear
                                    </button>
                                </form>
                            </div>
                            {% endif %}
                        </form>
                    </div>

                    <!-- Submissions Upload -->
                    <div class="upload-section-compact">
                        <div class="upload-header-compact">
                            <h5>Student Submissions</h5>
                            {% if session.get('submissions') or session.get('last_submission') %}
                            <span class="status-badge success">
                                <i class="bi bi-check-circle"></i> {{ session.get('submissions', [])|length or 1 }} files
                            </span>
                            {% else %}
                            <span class="status-badge pending">
                                <i class="bi bi-clock"></i> Pending
                            </span>
                            {% endif %}
                        </div>
                        <form action="{{ url_for('upload_submission') }}" method="post" enctype="multipart/form-data" id="submissionForm">
                            <div class="upload-zone-compact" onclick="document.getElementById('submissionFile').click()">
                                <input type="file" id="submissionFile" name="file" accept=".docx,.txt,.pdf,.jpg,.jpeg,.png,.tiff,.bmp,.gif" multiple style="display: none;">
                                <i class="bi bi-files upload-icon-compact"></i>
                                <span>Click to upload submissions</span>
                            </div>
                            {% if session.get('submissions') or session.get('last_submission') %}
                            <div class="action-buttons-compact">
                                <a href="{{ url_for('view_submission') }}" class="btn-compact btn-secondary">
                                    <i class="bi bi-eye"></i> View All
                                </a>
                                <form action="{{ url_for('clear_submission') }}" method="post" style="display: inline;">
                                    <button type="submit" class="btn-compact btn-danger" data-confirm="Clear submissions?">
                                        <i class="bi bi-trash"></i> Clear
                                    </button>
                                </form>
                            </div>
                            {% endif %}
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Processing Section -->
        <div class="col-lg-4">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-robot"></i> AI Processing</h4>
                    <p class="card-subtitle">Grade with AI analysis</p>
                </div>
                <div class="card-content">
                    <form action="{{ url_for('grade_submission') }}" method="post" class="processing-form-compact">
                        <div class="form-group-compact mb-3">
                            <label>Questions to Answer</label>
                            <input type="number" class="form-control-compact" name="num_questions" min="1" value="1">
                        </div>

                        {% if session.get('submissions') and session.get('submissions')|length > 1 %}
                        <div class="form-group-compact mb-3">
                            <label>Select Submission</label>
                            <select class="form-control-compact" name="submission_id">
                                {% for sub in session.get('submissions', []) %}
                                <option value="{{ sub.filename }}">{{ sub.filename }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}

                        <div class="processing-actions-compact">
                            <button type="submit" class="btn-primary-compact"
                                    {{ 'disabled' if not session.get('guide_uploaded') or (not session.get('last_submission') and not session.get('submissions')) }}>
                                <i class="bi bi-play-circle"></i>
                                Process & Grade
                            </button>
                        </div>
                    </form>

                    {% if session.get('submissions') and session.get('submissions')|length > 1 %}
                    <div class="batch-section-compact mt-3">
                        <form action="{{ url_for('batch_grade') }}" method="post">
                            <input type="hidden" name="num_questions" value="1">
                            <button type="submit" class="btn-success-compact"
                                    {{ 'disabled' if not session.get('guide_uploaded') or session.get('submissions')|length < 2 }}>
                                <i class="bi bi-layers"></i>
                                Process All
                            </button>
                        </form>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Results & Management Section -->
        <div class="col-lg-3">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-graph-up"></i> Results</h4>
                    <p class="card-subtitle">View and manage</p>
                </div>
                <div class="card-content">
                    {% if session.get('last_grading_result') or session.get('batch_results') %}
                    <div class="results-section-compact">
                        <div class="results-grid-compact">
                            {% if session.get('last_grading_result') %}
                            <a href="{{ url_for('view_results') }}" class="result-card-compact">
                                <i class="bi bi-file-text"></i>
                                <span>View Results</span>
                            </a>
                            {% endif %}
                            {% if session.get('batch_results') %}
                            <a href="{{ url_for('view_batch_results') }}" class="result-card-compact">
                                <i class="bi bi-collection"></i>
                                <span>Batch Results</span>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    {% else %}
                    <div class="no-results-compact">
                        <i class="bi bi-graph-up-arrow"></i>
                        <p>No results yet</p>
                        <small>Process submissions to see results</small>
                    </div>
                    {% endif %}

                    <div class="management-section-compact mt-4">
                        <form action="{{ url_for('clear_all') }}" method="post" id="clearAllForm">
                            <button type="submit" class="btn-warning-compact" id="clearAllButton"
                                    data-confirm="Clear all data and cache files? This cannot be undone."
                                    data-loading-text="Clearing...">
                                <i class="bi bi-trash"></i> Clear All Data
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Loaders are now handled by the loader.js component -->
{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard specific styles */
    .submission-list {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid var(--gray-200);
        border-radius: var(--border-radius);
        background: var(--gray-50);
    }

    .submission-item {
        transition: var(--transition);
        padding: 0.75rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .submission-item:hover {
        background-color: rgba(79, 70, 229, 0.05);
    }

    .submission-item:last-child {
        border-bottom: none !important;
    }

    .submission-filename {
        font-weight: 500;
        color: var(--gray-700);
    }

    /* Enhanced card hover effects */
    .card:hover .card-header h5 i {
        transform: scale(1.1);
        transition: var(--transition);
    }

    /* Progress indicator styling */
    .progress {
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* Welcome section gradient text fallback */
    .display-4 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        color: transparent;
    }

    /* Status card enhancements */
    .status-card h6 {
        color: var(--gray-700);
        font-weight: 600;
    }

    /* Button group improvements */
    .btn-lg {
        padding: 1rem 2rem;
        font-size: 1rem;
        font-weight: 600;
    }

    /* Processing section styling */
    .card .row .col-md-6:first-child {
        border-right: 1px solid var(--gray-200);
    }

    @media (max-width: 768px) {
        .card .row .col-md-6:first-child {
            border-right: none;
            border-bottom: 1px solid var(--gray-200);
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Make file upload areas work
    document.addEventListener('DOMContentLoaded', function() {
        // Helper function to show loading
        function showLoading(text = 'Processing...') {
            const overlay = document.querySelector('.loading-overlay');
            const loadingText = document.querySelector('.loading-text');
            if (overlay && loadingText) {
                loadingText.textContent = text;
                overlay.style.display = 'flex';
            }
        }

        // File upload handlers
        const guideFileInput = document.getElementById('guideFile');
        if (guideFileInput) {
            guideFileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    showLoading('Uploading Marking Guide...');
                    // Submit the form
                    document.getElementById('guideForm').submit();
                }
            });
        }

        const submissionFileInput = document.getElementById('submissionFile');
        if (submissionFileInput) {
            submissionFileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    showLoading('Uploading Submission...');
                    // Submit the form
                    document.getElementById('submissionForm').submit();
                }
            });
        }

        // Make file upload areas clickable
        document.querySelectorAll('.file-upload-area').forEach(area => {
            area.addEventListener('click', function() {
                const fileInput = this.querySelector('input[type="file"]');
                if (fileInput) {
                    fileInput.click();
                }
            });
        });

        // Add confirmation for buttons
        document.querySelectorAll('[data-confirm]').forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm(this.getAttribute('data-confirm'))) {
                    e.preventDefault();
                }
            });
        });

        // Add loader for processing & grading (combined functionality)
        const gradeForm = document.querySelector('form[action*="grade_submission"]');
        if (gradeForm) {
            gradeForm.addEventListener('submit', function(e) {
                showLoading('AI Processing & Grading...');
                // Let the form submit normally
            });
        }

        // Add loader for batch grading
        const batchForm = document.querySelector('form[action*="batch_grade"]');
        if (batchForm) {
            batchForm.addEventListener('submit', function(e) {
                showLoading('Batch Grading in Progress...');
                // Let the form submit normally
            });
        }





        // Add loader for clear all button
        const clearAllForm = document.getElementById('clearAllForm');
        if (clearAllForm) {
            clearAllForm.addEventListener('submit', function(e) {
                showLoading('Clearing All Data...');
                // Let the form submit normally
            });
        }
    });
</script>
{% endblock %}