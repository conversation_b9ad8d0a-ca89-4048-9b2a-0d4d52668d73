{% extends "base.html" %}

{% block title %}Exam Grader - Home{% endblock %}

{% block content %}
<div class="text-center mb-5">
    <h1 class="display-4 fw-bold mb-2" style="letter-spacing:-1px;">Exam Grader</h1>
    <p class="lead mb-3 text-muted">AI-powered, fast, and reliable grading for your exams.<br>Upload your marking guide and student submission to get started!</p>
</div>

<!-- Progress Bar -->
<div class="d-flex justify-content-center mb-4">
    <div class="progress" style="width: 340px; height: 1.2rem; border-radius: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.04);">
        <div class="progress-bar bg-primary" role="progressbar" style="width: {{ progress_value }}%; border-radius: 1rem;" aria-valuenow="{{ progress_value }}" aria-valuemin="0" aria-valuemax="100"></div>
    </div>
</div>

<div class="row g-4">
    <!-- Marking Guide Upload -->
    <div class="col-md-6">
        <div class="card h-100 shadow-sm border-0 rounded-4">
            <div class="card-header bg-white border-0 rounded-top-4 pb-0">
                <h5 class="mb-0 fw-semibold text-primary"><i class="bi bi-journal-text me-2"></i>Upload Marking Guide</h5>
            </div>
            <div class="card-body pt-2">
                <form action="{{ url_for('upload_guide') }}" method="post" enctype="multipart/form-data" id="guideForm">
                    <div class="file-upload-area mb-3">
                        <input type="file" class="d-none" id="guideFile" name="file" accept=".docx,.txt">
                        <div class="file-upload-icon">
                            <i class="bi bi-cloud-arrow-up"></i>
                        </div>
                        <h5>Drop your marking guide here</h5>
                        <p class="text-muted">or click to browse files</p>
                        <p class="text-muted small">Supported formats: .docx, .txt</p>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="guideButton" data-loading-text="Uploading Guide...">
                            <i class="bi bi-upload me-1"></i> Upload Guide
                        </button>

                        {% if session.get('guide_uploaded') %}
                        <a href="{{ url_for('view_guide') }}" class="btn btn-outline-primary">
                            <i class="bi bi-eye me-1"></i> View Guide
                        </a>
                        <form action="{{ url_for('clear_guide') }}" method="post" class="d-grid">
                            <button type="submit" class="btn btn-outline-danger btn-sm" data-confirm="Are you sure you want to clear the current guide?">
                                <i class="bi bi-trash me-1"></i> Clear Guide
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </form>

                {% if session.get('guide_uploaded') %}
                <div class="alert alert-success mt-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <strong>Guide loaded!</strong>
                        </div>
                        <div class="text-end">
                            <div>Raw text extracted</div>
                            <small>{{ session.get('guide_content', '')|length }} characters</small>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Student Submission Upload -->
    <div class="col-md-6">
        <div class="card h-100 shadow-sm border-0 rounded-4">
            <div class="card-header bg-white border-0 rounded-top-4 pb-0">
                <h5 class="mb-0 fw-semibold text-info"><i class="bi bi-file-earmark-text me-2"></i>Upload Student Submission</h5>
            </div>
            <div class="card-body pt-2">
                <form action="{{ url_for('upload_submission') }}" method="post" enctype="multipart/form-data" id="submissionForm">
                    <div class="file-upload-area mb-3">
                        <input type="file" class="d-none" id="submissionFile" name="file" accept=".docx,.txt,.pdf,.jpg,.jpeg,.png,.tiff,.bmp,.gif" multiple>
                        <div class="file-upload-icon">
                            <i class="bi bi-cloud-arrow-up"></i>
                        </div>
                        <h5>Drop your student submissions here</h5>
                        <p class="text-muted">or click to browse files</p>
                        <p class="text-muted small">Supported formats: .docx, .txt, .pdf, images</p>
                        <p class="text-primary small"><i class="bi bi-info-circle"></i> You can select multiple files</p>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-info text-white" id="submissionButton"
                                {{ 'disabled' if not session.get('guide_uploaded') }}
                                data-loading-text="Uploading Submission...">
                            <i class="bi bi-upload me-1"></i> Upload Submission
                        </button>

                        {% if not session.get('guide_uploaded') %}
                        <div class="alert alert-warning mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Please upload a marking guide first
                        </div>
                        {% endif %}

                        {% if session.get('last_submission') %}
                        <a href="{{ url_for('view_submission') }}" class="btn btn-outline-info">
                            <i class="bi bi-eye me-1"></i> View Submission
                        </a>
                        <form action="{{ url_for('clear_submission') }}" method="post" class="d-grid">
                            <button type="submit" class="btn btn-outline-danger btn-sm" data-confirm="Are you sure you want to clear the current submission?">
                                <i class="bi bi-trash me-1"></i> Clear Submission
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </form>

                {% if session.get('submissions') %}
                <div class="alert alert-success mt-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <strong>{{ session.get('submissions')|length }} Submission(s) loaded!</strong>
                        </div>
                        <div class="text-end">
                            <a href="{{ url_for('view_submission') }}" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-eye me-1"></i> View All
                            </a>
                        </div>
                    </div>
                </div>
                <div class="submission-list mt-3">
                    {% for submission in session.get('submissions', []) %}
                    <div class="submission-item p-2 border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="bi bi-file-earmark-text me-2"></i>
                                <span class="submission-filename">{{ submission.filename }}</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <small class="text-muted me-2">{{ "%.1f"|format(submission.raw_text|length / 1024) }} KB</small>
                                <form action="{{ url_for('remove_submission', filename=submission.filename) }}" method="post" class="d-inline">
                                    <button type="submit" class="btn btn-sm btn-outline-danger"
                                            data-confirm="Are you sure you want to remove this submission?">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                {% if session.get('last_submission') and not session.get('submissions') %}
                <div class="alert alert-success mt-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <strong>Submission loaded!</strong>
                        </div>
                        <div class="text-end">
                            <small>{{ session.get('last_submission', {}).get('filename', '') }}</small>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Processing Section -->
<div class="row mt-4 g-4">
    <div class="col-md-6">
        <div class="card h-100 shadow-sm border-0 rounded-4">
            <div class="card-header bg-white border-0 rounded-top-4 pb-0">
                <h5 class="mb-0 fw-semibold text-success"><i class="bi bi-diagram-3 me-2"></i>Map Submission to Guide</h5>
            </div>
            <div class="card-body pt-2">
                <p>Map student submission sections to marking guide criteria using AI.</p>
                <p class="text-muted small">This helps visualize how each part of the submission relates to specific criteria in the guide.</p>

                <form action="{{ url_for('map_submission') }}" method="post" class="d-grid gap-2">
                    <div class="mb-3">
                        <label for="num_questions" class="form-label">Number of questions to answer:</label>
                        <input type="number" class="form-control" id="num_questions" name="num_questions" min="1" value="1">
                        <div class="form-text">Specify how many questions the student should answer from the marking guide.</div>
                    </div>

                    {% if session.get('submissions') and session.get('submissions')|length > 1 %}
                    <div class="mb-3">
                        <label for="submission_id" class="form-label">Select Submission to Map</label>
                        <select class="form-select" id="submission_id" name="submission_id">
                            {% for sub in session.get('submissions', []) %}
                            <option value="{{ sub.filename }}">{{ sub.filename }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            Select which submission to map. You can map multiple submissions separately.
                        </div>
                    </div>
                    {% endif %}

                    <button type="submit" class="btn btn-success"
                            {{ 'disabled' if not session.get('guide_uploaded') or (not session.get('last_submission') and not session.get('submissions')) }}
                            data-loading-text="Mapping Submission...">
                        <i class="bi bi-diagram-3 me-1"></i> Map Submission
                    </button>

                    {% if session.get('last_mapping_result') %}
                    <a href="{{ url_for('view_mapping') }}" class="btn btn-outline-success">
                        <i class="bi bi-eye me-1"></i> View Mapping
                    </a>
                    {% endif %}

                    <!-- Map All Submissions Button -->
                    <div class="d-grid gap-2 mt-3">
                      <form action="{{ url_for('map_all_submissions') }}" method="post" class="d-grid">
                        <input type="hidden" name="num_questions" value="{{ session.get('num_questions', 1) }}">
                        <button type="submit" class="btn btn-success"
                                {{ 'disabled' if not session.get('guide_uploaded') or not session.get('submissions') or session.get('submissions')|length < 2 }}
                                data-loading-text="Mapping All Submissions...">
                          <i class="bi bi-diagram-3 me-1"></i> Map All Submissions
                        </button>
                      </form>

                      {% if not session.get('guide_uploaded') or not session.get('submissions') or session.get('submissions')|length < 2 %}
                      <div class="alert alert-info small mb-0">
                        <i class="bi bi-info-circle me-1"></i>
                        Upload a marking guide and at least 2 submissions to enable batch mapping
                      </div>
                      {% endif %}

                      {% if session.get('batch_mapping_results') %}
                      <a href="{{ url_for('view_batch_mappings') }}" class="btn btn-outline-success">
                        <i class="bi bi-eye me-1"></i> View All Mappings
                      </a>
                      {% endif %}
                    </div>
                </form>

                {% if not session.get('guide_uploaded') or (not session.get('last_submission') and not session.get('submissions')) %}
                <div class="alert alert-warning mt-3">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Please upload both a marking guide and at least one student submission first
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card h-100 shadow-sm border-0 rounded-4">
            <div class="card-header bg-white border-0 rounded-top-4 pb-0">
                <h5 class="mb-0 fw-semibold text-primary"><i class="bi bi-award me-2"></i>Grade Submission</h5>
            </div>
            <div class="card-body pt-2">
                <p>Grade the student submission against the marking guide using AI.</p>
                <p class="text-muted small">The AI will evaluate the submission, provide scores, and give detailed feedback.</p>

                <form action="{{ url_for('grade_submission') }}" method="post" class="d-grid gap-2">
                    {% if session.get('submissions') and session.get('submissions')|length > 1 %}
                    <div class="mb-3">
                        <label for="submission_id" class="form-label">Select Submission to Grade</label>
                        <select class="form-select" id="submission_id" name="submission_id">
                            {% for sub in session.get('submissions', []) %}
                            <option value="{{ sub.filename }}">{{ sub.filename }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            Select which submission to grade. You can grade multiple submissions separately.
                        </div>
                    </div>
                    {% endif %}

                    <button type="submit" class="btn btn-primary"
                            {{ 'disabled' if not session.get('guide_uploaded') or (not session.get('last_submission') and not session.get('submissions')) }}
                            data-loading-text="Grading Submission...">
                        <i class="bi bi-award me-1"></i> Grade Submission
                    </button>

                    {% if session.get('last_grading_result') %}
                    <a href="{{ url_for('view_results') }}" class="btn btn-outline-primary">
                        <i class="bi bi-eye me-1"></i> View Results
                    </a>
                    {% endif %}

                    <!-- Batch Grading Button -->
                    <div class="d-grid gap-2 mt-3">
                      <form action="{{ url_for('batch_grade') }}" method="post" class="d-grid">
                        <input type="hidden" name="num_questions" value="{{ session.get('num_questions', 1) }}">
                        <button type="submit" class="btn btn-primary"
                                {{ 'disabled' if not session.get('guide_uploaded') or not session.get('submissions') or session.get('submissions')|length < 2 }}
                                data-loading-text="Batch Grading in Progress...">
                          <i class="bi bi-people me-1"></i> Grade All Submissions
                        </button>
                      </form>

                      {% if not session.get('guide_uploaded') or not session.get('submissions') or session.get('submissions')|length < 2 %}
                      <div class="alert alert-info small mb-0">
                        <i class="bi bi-info-circle me-1"></i>
                        Upload a marking guide and at least 2 submissions to enable batch grading
                      </div>
                      {% endif %}

                      {% if session.get('batch_results') %}
                      <a href="{{ url_for('view_batch_results') }}" class="btn btn-outline-primary">
                        <i class="bi bi-eye me-1"></i> View Batch Results
                      </a>
                      {% endif %}
                    </div>
                </form>

                {% if not session.get('guide_uploaded') or (not session.get('last_submission') and not session.get('submissions')) %}
                <div class="alert alert-warning mt-3">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Please upload both a marking guide and at least one student submission first
                </div>
                {% endif %}

                {% if session.get('last_grading_result') %}
                <div class="alert alert-success mt-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <strong>Grading complete!</strong>
                        </div>
                        <div>
                            <span class="badge bg-success">Score: {{ session.get('last_score', 0) }}%</span>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="card mt-4 shadow-sm border-0 rounded-4">
    <div class="card-header bg-white border-0 rounded-top-4 pb-0">
        <h5 class="mb-0 fw-semibold text-secondary"><i class="bi bi-info-circle me-2"></i>System Status</h5>
    </div>
    <div class="card-body pt-2">
        <div class="row">
            <div class="col-md-4 mb-3 mb-md-0">
                <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                    <div>
                        <h6 class="mb-0">OCR Service</h6>
                        <small class="text-muted">For image processing</small>
                    </div>
                    <div>
                        {% if ocr_status %}
                        <span class="badge bg-success"><i class="bi bi-check-circle"></i> Available</span>
                        {% else %}
                        <span class="badge bg-danger"><i class="bi bi-x-circle"></i> Unavailable</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3 mb-md-0">
                <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                    <div>
                        <h6 class="mb-0">DeepSeek LLM</h6>
                        <small class="text-muted">AI grading engine</small>
                    </div>
                    <div>
                        {% if llm_status %}
                        <span class="badge bg-success"><i class="bi bi-check-circle"></i> Available</span>
                        {% else %}
                        <span class="badge bg-danger"><i class="bi bi-x-circle"></i> Unavailable</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                    <div>
                        <h6 class="mb-0">Storage</h6>
                        <small class="text-muted">Cache status</small>
                    </div>
                    <div>
                        {% if storage_stats %}
                        <span class="badge bg-info">{{ "%.1f"|format(storage_stats.total_size_mb) }} / {{ storage_stats.max_size_mb|int }} MB</span>
                        {% else %}
                        <span class="badge bg-secondary"><i class="bi bi-question-circle"></i> Unknown</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Management -->
<div class="card mt-4 shadow-sm border-0 rounded-4">
    <div class="card-header bg-white border-0 rounded-top-4 pb-0">
        <h5 class="mb-0 fw-semibold text-danger"><i class="bi bi-trash me-2"></i>Data Management</h5>
    </div>
    <div class="card-body pt-2">
        <div class="row align-items-center">
            <div class="col-md-8">
                <p class="mb-0">Clear all session data, uploaded files, and cache files to free up space and start fresh.</p>
                <p class="text-muted small">This will remove all uploaded guides, submissions, results, and temporary files.</p>
            </div>
            <div class="col-md-4 text-end">
                <form action="{{ url_for('clear_all') }}" method="post" id="clearAllForm">
                    <button type="submit" class="btn btn-danger" id="clearAllButton"
                            data-confirm="Are you sure you want to clear all data and cache files? This action cannot be undone."
                            data-loading-text="Clearing Data...">
                        <i class="bi bi-trash me-1"></i> Clear All Data & Cache
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Loaders are now handled by the loader.js component -->
{% endblock %}

{% block extra_css %}
<style>
    .submission-list {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }

    .submission-item {
        transition: background-color 0.2s;
    }

    .submission-item:hover {
        background-color: rgba(13, 202, 240, 0.1);
    }

    .submission-item:last-child {
        border-bottom: none !important;
    }

    .submission-filename {
        font-weight: 500;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Make file upload areas work
    document.addEventListener('DOMContentLoaded', function() {
        // File upload handlers
        document.getElementById('guideFile').addEventListener('change', function() {
            if (this.files.length > 0) {
                // Show loader for guide upload
                const guideButton = document.getElementById('guideButton');
                toggleButtonLoader(guideButton, true);

                // Create a simple loader
                const loader = new Loader({
                    type: 'spinner',
                    text: 'Uploading Marking Guide...',
                    subText: 'Please wait while we process your file'
                });
                loader.show();

                // Submit the form
                document.getElementById('guideForm').submit();
            }
        });

        document.getElementById('submissionFile').addEventListener('change', function() {
            if (this.files.length > 0) {
                // Show loader for submission upload
                const submissionButton = document.getElementById('submissionButton');
                toggleButtonLoader(submissionButton, true);

                // Create a simple loader
                const loader = new Loader({
                    type: 'pulse',
                    text: 'Uploading Submission...',
                    subText: 'This may take a moment if OCR is needed'
                });
                loader.show();

                // Submit the form
                document.getElementById('submissionForm').submit();
            }
        });

        // Make file upload areas clickable
        document.querySelectorAll('.file-upload-area').forEach(area => {
            area.addEventListener('click', function() {
                const fileInput = this.querySelector('input[type="file"]');
                if (fileInput) {
                    fileInput.click();
                }
            });
        });

        // Add confirmation for buttons
        document.querySelectorAll('[data-confirm]').forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm(this.getAttribute('data-confirm'))) {
                    e.preventDefault();
                }
            });
        });

        // Add loader for mapping
        document.querySelector('form[action*="map_submission"]')?.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show button loader
            const mapButton = this.querySelector('button[type="submit"]');
            toggleButtonLoader(mapButton, true);

            // Create a loader with appropriate messaging
            const loader = new Loader({
                type: 'dots',
                text: 'Mapping Submission...',
                subText: 'This may take a minute or two'
            });
            loader.show();

            // Create a progress tracker just to get a tracker ID (but we won't display it)
            fetch('/api/progress', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation_type: 'llm',
                    task_name: 'Mapping Submission',
                    total_steps: 100
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.id) {
                    // Start tracking progress
                    progressTracker.startTracking(data.id, 'llm');

                    // Submit the form
                    this.submit();
                } else {
                    // Submit the form even if we couldn't create a tracker
                    this.submit();
                }
            })
            .catch(error => {
                console.error('Error creating progress tracker:', error);
                // Submit the form even if there was an error
                this.submit();
            });
        });

        // Add loader for batch grading
        document.querySelector('form[action*="batch_grade"]')?.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show button loader
            const batchButton = this.querySelector('button[type="submit"]');
            toggleButtonLoader(batchButton, true);

            // Create a loader with appropriate messaging
            const loader = new Loader({
                type: 'dots',
                text: 'Batch Grading in Progress...',
                subText: 'This may take several minutes for multiple submissions'
            });
            loader.show();

            // Create a progress tracker
            fetch('/api/progress', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation_type: 'batch_grading',
                    task_name: 'Batch Grading',
                    total_steps: 100
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.id) {
                    // Start tracking progress
                    progressTracker.startTracking(data.id, 'batch_grading');

                    // Submit the form
                    this.submit();
                } else {
                    // If we couldn't create a tracker, still submit the form
                    this.submit();
                }
            })
            .catch(error => {
                console.error('Error creating progress tracker:', error);
                // Still submit the form even if tracker creation fails
                this.submit();
            });
        });

        // Add loader for map all submissions
        document.querySelector('form[action*="map_all_submissions"]')?.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show button loader
            const mapAllButton = this.querySelector('button[type="submit"]');
            toggleButtonLoader(mapAllButton, true);

            // Create a loader with appropriate messaging
            const loader = new Loader({
                type: 'dots',
                text: 'Mapping All Submissions...',
                subText: 'This may take several minutes for multiple submissions'
            });
            loader.show();

            // Create a progress tracker
            fetch('/api/progress', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation_type: 'batch_mapping',
                    task_name: 'Mapping All Submissions',
                    total_steps: 100
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.id) {
                    // Start tracking progress
                    progressTracker.startTracking(data.id, 'batch_mapping');

                    // Submit the form
                    this.submit();
                } else {
                    // If we couldn't create a tracker, still submit the form
                    this.submit();
                }
            })
            .catch(error => {
                console.error('Error creating progress tracker:', error);
                // Still submit the form even if tracker creation fails
                this.submit();
            });
        });

        // Add loader for grading
        document.querySelector('form[action*="grade_submission"]')?.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show button loader
            const gradeButton = this.querySelector('button[type="submit"]');
            toggleButtonLoader(gradeButton, true);

            // Create a loader with appropriate messaging
            const loader = new Loader({
                type: 'dots',
                text: 'Grading Submission...',
                subText: 'This may take a minute or two'
            });
            loader.show();

            // Create a progress tracker
            fetch('/api/progress', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation_type: 'llm',
                    task_name: 'Grading Submission',
                    total_steps: 3
                })
            })
            .then(response => response.json())
            .then(data => {
                // Update the loader with more specific information
                loader.updateText('AI Processing Submission...', 'Analyzing content and mapping to guide');

                // Submit the form
                this.submit();
            })
            .catch(error => {
                console.error('Error creating progress tracker:', error);

                // Update the loader with generic information
                loader.updateText('Mapping Submission...', 'Processing your request');

                // Submit the form anyway
                this.submit();
            });
        });

        document.querySelector('form[action*="grade_submission"]')?.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show button loader
            const gradeButton = this.querySelector('button[type="submit"]');
            toggleButtonLoader(gradeButton, true);

            // Create a loader with appropriate messaging
            const loader = new Loader({
                type: 'dots',
                text: 'Grading Submission...',
                subText: 'This may take a minute or two'
            });
            loader.show();

            // Create a progress tracker just to get a tracker ID (but we won't display it)
            fetch('/api/progress', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation_type: 'llm',
                    task_name: 'Grading Submission',
                    total_steps: 5
                })
            })
            .then(response => response.json())
            .then(data => {
                // Update the loader with more specific information
                loader.updateText('AI Evaluating Submission...', 'Analyzing answers and calculating scores');

                // Submit the form
                this.submit();
            })
            .catch(error => {
                console.error('Error creating progress tracker:', error);

                // Update the loader with generic information
                loader.updateText('Grading Submission...', 'Processing your request');

                // Submit the form anyway
                this.submit();
            });
        });

        // Add loader for clear all button
        document.getElementById('clearAllForm')?.addEventListener('submit', function(e) {
            // This will only run after the user has confirmed
            const clearButton = document.getElementById('clearAllButton');
            toggleButtonLoader(clearButton, true);

            // Show a loader
            const loader = new Loader({
                type: 'spinner',
                text: 'Clearing All Data...',
                subText: 'Removing all files and cache'
            });
            loader.show();
        });
    });
</script>
{% endblock %}