[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "exam_grader"
version = "0.1.0"
authors = [
    { name="Exam Grader Team", email="<EMAIL>" }
]
description = "A tool for parsing and grading exam submissions using OCR"
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Development Status :: 4 - Beta",
    "Intended Audience :: Education",
    "Topic :: Education :: Testing",
]

dependencies = [
    "requests>=2.31.0",
    "python-dotenv>=1.0.1",
    "numpy>=1.26.4",
    "pandas>=2.2.0",
    "PyMuPDF>=1.23.8",
    "python-docx>=1.1.0",
    "Pillow>=10.2.0",
    "nltk>=3.8.1",
    "scikit-learn>=1.4.0",
    "Flask>=3.0.2",
    "Werkzeug>=3.0.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "black>=24.2.0",
    "isort>=5.13.2",
    "flake8>=7.0.0",
    "mypy>=1.8.0",
    "pytest-cov>=4.1.0",
]

[tool.setuptools]
packages = ["src", "utils"]

[tool.black]
line-length = 88
target-version = ["py38"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --cov=src --cov=utils"
testpaths = [
    "tests",
] 