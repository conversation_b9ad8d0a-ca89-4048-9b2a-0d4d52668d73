{% extends "base.html" %}

{% block title %}Help - Exam Grader{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="bi bi-question-circle-fill"></i> Help & Documentation</h1>
    <p class="page-subtitle">Learn how to use the Exam Grader application effectively</p>
</div>

<div class="dashboard-grid">
    <!-- Quick Start Guide -->
    <div class="row g-3 mb-4">
        <div class="col-12">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-play-circle"></i> Quick Start Guide</h4>
                    <p class="card-subtitle">Get started with exam grading in 4 simple steps</p>
                </div>
                <div class="card-content">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="help-step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h5>Upload Marking Guide</h5>
                                    <p>Upload your marking guide in .docx or .txt format. This contains the questions and expected answers or grading criteria.</p>
                                    <small class="text-muted">Supported formats: .docx, .txt</small>
                                </div>
                            </div>
                            
                            <div class="help-step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h5>Upload Student Submissions</h5>
                                    <p>Upload one or more student submissions. The system supports multiple file formats including handwritten documents.</p>
                                    <small class="text-muted">Supported formats: .pdf, .docx, .txt, .jpg, .png</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="help-step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h5>Map & Grade</h5>
                                    <p>Use AI to automatically map student answers to marking guide questions and generate grades with detailed feedback.</p>
                                    <small class="text-muted">Powered by advanced AI technology</small>
                                </div>
                            </div>
                            
                            <div class="help-step">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h5>Review Results</h5>
                                    <p>Review the grading results, download detailed reports, and export grades to Excel for further analysis.</p>
                                    <small class="text-muted">Export options: Excel, detailed reports</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Features Overview -->
    <div class="row g-3 mb-4">
        <div class="col-lg-6">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-star"></i> Key Features</h4>
                    <p class="card-subtitle">What makes Exam Grader powerful</p>
                </div>
                <div class="card-content">
                    <div class="feature-list">
                        <div class="feature-item">
                            <i class="bi bi-robot text-primary"></i>
                            <div>
                                <h6>AI-Powered Grading</h6>
                                <p>Advanced AI analyzes student responses and provides accurate grades with detailed feedback.</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <i class="bi bi-eye text-success"></i>
                            <div>
                                <h6>Handwriting Recognition</h6>
                                <p>OCR technology extracts text from handwritten submissions and scanned documents.</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <i class="bi bi-layers text-info"></i>
                            <div>
                                <h6>Batch Processing</h6>
                                <p>Grade multiple student submissions simultaneously for efficient workflow.</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <i class="bi bi-file-earmark-excel text-warning"></i>
                            <div>
                                <h6>Excel Export</h6>
                                <p>Export detailed grading results to Excel for gradebook integration and analysis.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-file-text"></i> Supported File Formats</h4>
                    <p class="card-subtitle">Compatible file types for uploads</p>
                </div>
                <div class="card-content">
                    <div class="format-category">
                        <h6><i class="bi bi-journal-text text-primary"></i> Marking Guides</h6>
                        <ul class="format-list">
                            <li><strong>.docx</strong> - Microsoft Word documents</li>
                            <li><strong>.txt</strong> - Plain text files</li>
                        </ul>
                    </div>
                    
                    <div class="format-category">
                        <h6><i class="bi bi-file-earmark text-success"></i> Student Submissions</h6>
                        <ul class="format-list">
                            <li><strong>.pdf</strong> - PDF documents (text or scanned)</li>
                            <li><strong>.docx</strong> - Microsoft Word documents</li>
                            <li><strong>.txt</strong> - Plain text files</li>
                            <li><strong>.jpg, .jpeg</strong> - JPEG images</li>
                            <li><strong>.png</strong> - PNG images</li>
                            <li><strong>.tiff</strong> - TIFF images</li>
                            <li><strong>.bmp</strong> - Bitmap images</li>
                            <li><strong>.gif</strong> - GIF images</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- FAQ Section -->
    <div class="row g-3 mb-4">
        <div class="col-12">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-question-circle"></i> Frequently Asked Questions</h4>
                    <p class="card-subtitle">Common questions and answers</p>
                </div>
                <div class="card-content">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    How accurate is the AI grading?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    The AI grading system uses advanced language models to analyze student responses. While highly accurate for most content, we recommend reviewing AI-generated grades, especially for complex or subjective questions. The system provides detailed feedback to help you understand the grading rationale.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    What if the OCR can't read handwriting?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    The OCR system is optimized for handwriting recognition, but very poor handwriting may not be readable. In such cases, you can manually review the extracted text and make corrections. For best results, ensure good image quality and clear handwriting.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    Can I grade multiple students at once?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Yes! The batch grading feature allows you to upload multiple student submissions and grade them all simultaneously. This is perfect for large classes and saves significant time compared to individual grading.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                    How do I export grades to my gradebook?
                                </button>
                            </h2>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    After grading, use the "Download Excel" button to export results in Excel format. The exported file includes student names, scores, percentages, letter grades, and detailed feedback that can be imported into most gradebook systems.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
                                    What's the maximum file size I can upload?
                                </button>
                            </h2>
                            <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    The default maximum file size is 20MB per file. This can be adjusted in the Settings page. For very large files, consider splitting them into smaller parts or reducing image resolution while maintaining readability.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tips and Best Practices -->
    <div class="row g-3 mb-4">
        <div class="col-12">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-lightbulb"></i> Tips & Best Practices</h4>
                    <p class="card-subtitle">Get the most out of Exam Grader</p>
                </div>
                <div class="card-content">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📝 Marking Guide Tips</h6>
                            <ul class="tips-list">
                                <li>Include clear question numbers and point values</li>
                                <li>Provide detailed expected answers or rubrics</li>
                                <li>Use consistent formatting throughout</li>
                                <li>Include partial credit guidelines when applicable</li>
                            </ul>
                            
                            <h6>📄 Submission Tips</h6>
                            <ul class="tips-list">
                                <li>Ensure good image quality for scanned documents</li>
                                <li>Use high contrast (dark text on light background)</li>
                                <li>Avoid shadows and glare in photos</li>
                                <li>Keep file sizes reasonable for faster processing</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>⚡ Performance Tips</h6>
                            <ul class="tips-list">
                                <li>Use "Fast" model for quick grading of simple questions</li>
                                <li>Use "Accurate" model for complex or subjective content</li>
                                <li>Process smaller batches for faster results</li>
                                <li>Clear cache regularly to free up storage space</li>
                            </ul>
                            
                            <h6>✅ Quality Assurance</h6>
                            <ul class="tips-list">
                                <li>Always review AI-generated grades before finalizing</li>
                                <li>Check OCR-extracted text for accuracy</li>
                                <li>Use the detailed feedback to understand grading logic</li>
                                <li>Keep backup copies of original submissions</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.help-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.step-number {
    background: var(--primary-blue);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
    flex-shrink: 0;
}

.step-content h5 {
    margin-bottom: 0.5rem;
    color: var(--primary-blue);
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.feature-item i {
    font-size: 1.5rem;
    margin-right: 1rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.feature-item h6 {
    margin-bottom: 0.25rem;
}

.feature-item p {
    margin-bottom: 0;
    font-size: 0.9rem;
    color: #6c757d;
}

.format-category {
    margin-bottom: 1.5rem;
}

.format-category h6 {
    margin-bottom: 0.5rem;
    color: var(--primary-blue);
}

.format-list {
    list-style: none;
    padding-left: 0;
}

.format-list li {
    padding: 0.25rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.format-list li:last-child {
    border-bottom: none;
}

.tips-list {
    list-style: none;
    padding-left: 0;
}

.tips-list li {
    padding: 0.25rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.tips-list li:before {
    content: "✓";
    color: var(--primary-blue);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.accordion-button {
    background-color: var(--primary-blue-50);
    color: var(--primary-blue);
}

.accordion-button:not(.collapsed) {
    background-color: var(--primary-blue);
    color: white;
}
</style>
{% endblock %}
