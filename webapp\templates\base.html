<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}Exam Grader{% endblock %}</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Bootstrap Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
    />

    <!-- Font Awesome Icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <!-- Custom Styles -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />

    {% block extra_css %}{% endblock %}
  </head>
  <body>
    <div class="app-container">
      <!-- Sidebar Navigation -->
      <aside class="sidebar">
        <div class="sidebar-header">
          <div class="logo">
            <i class="bi bi-mortarboard-fill"></i>
            <span>Exam Grader</span>
          </div>
        </div>

        <nav class="sidebar-nav">
          <a
            href="{{ url_for('index') }}"
            class="nav-item {% if request.endpoint == 'index' %}active{% endif %}"
          >
            <i class="bi bi-house-fill"></i>
            <span>Dashboard</span>
          </a>
          <a
            href="{{ url_for('view_mapping') }}"
            class="nav-item {% if request.endpoint == 'view_mapping' %}active{% endif %}"
          >
            <i class="bi bi-diagram-3-fill"></i>
            <span>Answer Mapping</span>
          </a>
          <a
            href="{{ url_for('view_results') }}"
            class="nav-item {% if request.endpoint == 'view_results' %}active{% endif %}"
          >
            <i class="bi bi-bar-chart-fill"></i>
            <span>Results</span>
          </a>
          <a
            href="{{ url_for('view_batch_results') }}"
            class="nav-item {% if request.endpoint == 'view_batch_results' %}active{% endif %}"
          >
            <i class="bi bi-layers-fill"></i>
            <span>Batch Results</span>
          </a>
          <a
            href="{{ url_for('settings') }}"
            class="nav-item {% if request.endpoint == 'settings' %}active{% endif %}"
          >
            <i class="bi bi-gear-fill"></i>
            <span>Settings</span>
          </a>
          <a
            href="{{ url_for('help_page') }}"
            class="nav-item {% if request.endpoint == 'help_page' %}active{% endif %}"
          >
            <i class="bi bi-question-circle-fill"></i>
            <span>Help</span>
          </a>
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="main-content">
        <!-- Top Bar -->
        <header class="topbar">
          <div class="page-title">
            <button class="menu-toggle">
              <i class="bi bi-list"></i>
            </button>
            <h1>{% block header %}Dashboard{% endblock %}</h1>
          </div>
          <div class="topbar-actions">
            {% block header_actions %}{% endblock %}
            <button class="btn-icon" id="notificationsBtn">
              <i class="bi bi-bell"></i>
            </button>
            <button class="btn-icon" id="userProfileBtn">
              <i class="bi bi-person-circle"></i>
            </button>
          </div>
        </header>

        <!-- Toast Container -->
        <div class="toast-container">
          {% with messages = get_flashed_messages(with_categories=true) %} {% if
          messages %} {% for category, message in messages %}
          <div class="toast toast-{{ category }}">
            <i
              class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-circle' }}"
            ></i>
            <span>{{ message }}</span>
          </div>
          {% endfor %} {% endif %} {% endwith %}
        </div>

        <!-- Page Content -->
        <div class="page-content">{% block content %}{% endblock %}</div>
      </main>
    </div>

    <!-- Mobile Menu Backdrop -->
    <div class="menu-backdrop"></div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" style="display: none">
      <div class="spinner"></div>
      <p class="loading-text">Processing...</p>
    </div>

    <!-- JavaScript for UI Interactions -->
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        // Mobile menu functionality
        const menuToggle = document.querySelector(".menu-toggle");
        const sidebar = document.querySelector(".sidebar");
        const backdrop = document.querySelector(".menu-backdrop");

        function toggleMenu() {
          sidebar.classList.toggle("show");
          backdrop.classList.toggle("show");
          document.body.style.overflow = sidebar.classList.contains("show")
            ? "hidden"
            : "";
        }

        menuToggle?.addEventListener("click", toggleMenu);
        backdrop?.addEventListener("click", toggleMenu);

        // Close menu on window resize
        window.addEventListener("resize", () => {
          if (window.innerWidth > 1024 && sidebar.classList.contains("show")) {
            toggleMenu();
          }
        });

        // Toast notifications
        const toasts = document.querySelectorAll(".toast");
        toasts.forEach((toast) => {
          setTimeout(() => {
            toast.style.animation = "slideOut 0.3s ease-out forwards";
            setTimeout(() => toast.remove(), 300);
          }, 5000);
        });

        // Loading State
        function showLoading() {
          document.querySelector(".loading-overlay").style.display = "flex";
        }

        function hideLoading() {
          document.querySelector(".loading-overlay").style.display = "none";
        }

        // Add loading state to forms
        document.querySelectorAll("form").forEach((form) => {
          form.addEventListener("submit", () => {
            showLoading();
          });
        });
      });
    </script>
    {% block scripts %}{% endblock %}
  </body>
</html>
