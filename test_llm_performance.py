#!/usr/bin/env python3
"""
LLM Performance Test Script

This script tests the performance improvements made to the LLM service.
Run this to verify that the optimizations are working correctly.
"""

import os
import sys
import time
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from services.llm_service_latest import LLMService, LLMServiceError
from config.llm_performance import get_profile, get_task_settings

def test_connection_speed():
    """Test connection speed with optimized settings."""
    print("🚀 Testing LLM Connection Speed...")
    
    try:
        # Test with speed profile
        speed_profile = get_profile("speed")
        llm_service = LLMService(**speed_profile)
        
        start_time = time.time()
        success = llm_service.test_connection()
        end_time = time.time()
        
        if success:
            print(f"✅ Connection successful in {end_time - start_time:.2f} seconds")
            print(f"📊 Model: {llm_service.model}")
            print(f"📊 Temperature: {llm_service.temperature}")
            print(f"📊 Timeout: {llm_service.timeout}s")
            print(f"📊 Max tokens: {llm_service.max_tokens}")
            print(f"📊 Deterministic: {llm_service.deterministic}")
            return True
        else:
            print("❌ Connection failed")
            return False
            
    except LLMServiceError as e:
        print(f"❌ LLM Service Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_guide_type_determination():
    """Test guide type determination speed."""
    print("\n🔍 Testing Guide Type Determination Speed...")
    
    sample_guide = """
    Question 1: What is the capital of France?
    Question 2: Explain the process of photosynthesis.
    Question 3: Calculate the area of a circle with radius 5cm.
    """
    
    try:
        speed_profile = get_profile("speed")
        llm_service = LLMService(**speed_profile)
        
        # Import mapping service
        from services.mapping_service import MappingService
        mapping_service = MappingService(llm_service=llm_service)
        
        start_time = time.time()
        guide_type, confidence, reasoning = mapping_service._determine_guide_type(sample_guide)
        end_time = time.time()
        
        print(f"✅ Guide type determined in {end_time - start_time:.2f} seconds")
        print(f"📊 Type: {guide_type}")
        print(f"📊 Confidence: {confidence}")
        print(f"📊 Reasoning: {reasoning}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_performance_profiles():
    """Test different performance profiles."""
    print("\n⚡ Testing Performance Profiles...")
    
    profiles = ["speed", "balanced", "accuracy"]
    
    for profile_name in profiles:
        print(f"\n🔧 Testing {profile_name.upper()} profile:")
        profile = get_profile(profile_name)
        
        try:
            llm_service = LLMService(**profile)
            print(f"  ✅ {profile_name} profile initialized successfully")
            print(f"     Model: {profile['model']}")
            print(f"     Timeout: {profile['timeout']}s")
            print(f"     Max tokens: {profile['max_tokens']}")
            print(f"     Deterministic: {profile['deterministic']}")
            
        except Exception as e:
            print(f"  ❌ {profile_name} profile failed: {e}")

def test_task_settings():
    """Test task-specific settings."""
    print("\n🎯 Testing Task-Specific Settings...")
    
    tasks = ["guide_type", "mapping", "grading", "test"]
    
    for task in tasks:
        settings = get_task_settings(task)
        print(f"  📋 {task.upper()}:")
        print(f"     Max tokens: {settings['max_tokens']}")
        print(f"     Timeout: {settings['timeout']}s")
        print(f"     Content limit: {settings['content_limit']} chars")

def main():
    """Run all performance tests."""
    print("🧪 LLM Performance Test Suite")
    print("=" * 50)
    
    # Check if API key is configured
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("❌ DEEPSEEK_API_KEY not found in environment variables")
        print("Please set your DeepSeek API key in the .env file")
        return False
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    if test_connection_speed():
        tests_passed += 1
    
    if test_guide_type_determination():
        tests_passed += 1
    
    test_performance_profiles()
    tests_passed += 1
    
    test_task_settings()
    tests_passed += 1
    
    print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests completed")
    
    if tests_passed == total_tests:
        print("🎉 All performance optimizations are working correctly!")
        print("\n💡 Performance Tips:")
        print("   - Use 'speed' profile for fastest responses")
        print("   - Use 'balanced' profile for good speed/accuracy balance")
        print("   - Use 'accuracy' profile when precision is critical")
        print("   - Monitor timeout settings based on your network speed")
    else:
        print("⚠️  Some tests failed. Check your configuration.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    main()
