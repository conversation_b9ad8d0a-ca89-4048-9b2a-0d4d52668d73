{% extends "base.html" %}

{% block title %}Settings - Exam Grader{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="bi bi-gear-fill"></i> Settings</h1>
    <p class="page-subtitle">Configure application preferences and system settings</p>
</div>

<div class="dashboard-grid">
    <!-- Performance Settings -->
    <div class="row g-3 mb-4">
        <div class="col-lg-6">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-speedometer2"></i> Performance Settings</h4>
                    <p class="card-subtitle">Optimize processing speed and accuracy</p>
                </div>
                <div class="card-content">
                    <form id="performanceForm">
                        <div class="mb-3">
                            <label for="llmModel" class="form-label">LLM Model</label>
                            <select class="form-select" id="llmModel" name="llm_model">
                                <option value="deepseek-chat">DeepSeek Chat (Fast)</option>
                                <option value="deepseek-reasoner">DeepSeek Reasoner (Accurate)</option>
                            </select>
                            <div class="form-text">Choose between speed and accuracy</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="processingTimeout" class="form-label">Processing Timeout</label>
                            <select class="form-select" id="processingTimeout" name="timeout">
                                <option value="15">15 seconds (Fast)</option>
                                <option value="30" selected>30 seconds (Standard)</option>
                                <option value="60">60 seconds (Extended)</option>
                            </select>
                            <div class="form-text">Maximum time to wait for AI responses</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="maxTokens" class="form-label">Response Length</label>
                            <select class="form-select" id="maxTokens" name="max_tokens">
                                <option value="500">Short (500 tokens)</option>
                                <option value="1000" selected>Medium (1000 tokens)</option>
                                <option value="2000">Long (2000 tokens)</option>
                            </select>
                            <div class="form-text">Maximum length of AI responses</div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="deterministicMode" name="deterministic">
                            <label class="form-check-label" for="deterministicMode">
                                Deterministic Mode
                            </label>
                            <div class="form-text">Enable for consistent results (slower)</div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Save Performance Settings
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-file-earmark-text"></i> File Processing</h4>
                    <p class="card-subtitle">Configure file upload and processing options</p>
                </div>
                <div class="card-content">
                    <form id="fileProcessingForm">
                        <div class="mb-3">
                            <label for="maxFileSize" class="form-label">Maximum File Size</label>
                            <select class="form-select" id="maxFileSize" name="max_file_size">
                                <option value="10">10 MB</option>
                                <option value="20" selected>20 MB</option>
                                <option value="50">50 MB</option>
                            </select>
                            <div class="form-text">Maximum size for uploaded files</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="ocrLanguage" class="form-label">OCR Language</label>
                            <select class="form-select" id="ocrLanguage" name="ocr_language">
                                <option value="en" selected>English</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                                <option value="de">German</option>
                                <option value="auto">Auto-detect</option>
                            </select>
                            <div class="form-text">Language for text recognition</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="ocrConfidence" class="form-label">OCR Confidence Threshold</label>
                            <input type="range" class="form-range" id="ocrConfidence" name="ocr_confidence" 
                                   min="0.5" max="1.0" step="0.1" value="0.7">
                            <div class="d-flex justify-content-between">
                                <small>0.5 (Permissive)</small>
                                <small>1.0 (Strict)</small>
                            </div>
                            <div class="form-text">Minimum confidence for OCR text extraction</div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="autoCache" name="auto_cache" checked>
                            <label class="form-check-label" for="autoCache">
                                Auto-save to Cache
                            </label>
                            <div class="form-text">Automatically cache processed files</div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Save File Settings
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Information -->
    <div class="row g-3 mb-4">
        <div class="col-12">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-info-circle"></i> System Information</h4>
                    <p class="card-subtitle">Current system status and configuration</p>
                </div>
                <div class="card-content">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Application Version</h6>
                            <p class="text-muted">Exam Grader v2.0.0</p>
                            
                            <h6>Python Version</h6>
                            <p class="text-muted">{{ python_version or "Unknown" }}</p>
                            
                            <h6>Cache Directory</h6>
                            <p class="text-muted">{{ cache_dir or "temp/cache" }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>AI Service Status</h6>
                            <p class="text-muted">
                                <span class="badge bg-success">Online</span>
                                DeepSeek API Connected
                            </p>
                            
                            <h6>OCR Service Status</h6>
                            <p class="text-muted">
                                <span class="badge bg-success">Available</span>
                                HandwritingOCR API
                            </p>
                            
                            <h6>Storage Usage</h6>
                            <p class="text-muted">{{ storage_usage or "0 MB" }} used</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Cache Management -->
    <div class="row g-3 mb-4">
        <div class="col-12">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-trash"></i> Cache Management</h4>
                    <p class="card-subtitle">Manage cached files and temporary data</p>
                </div>
                <div class="card-content">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5>Guide Cache</h5>
                                <p class="text-muted">{{ guide_cache_count or 0 }} files</p>
                                <button class="btn btn-outline-danger btn-sm" onclick="clearGuideCache()">
                                    <i class="bi bi-trash"></i> Clear Guide Cache
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5>Submission Cache</h5>
                                <p class="text-muted">{{ submission_cache_count or 0 }} files</p>
                                <button class="btn btn-outline-danger btn-sm" onclick="clearSubmissionCache()">
                                    <i class="bi bi-trash"></i> Clear Submission Cache
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5>All Cache</h5>
                                <p class="text-muted">{{ total_cache_size or "0 MB" }}</p>
                                <button class="btn btn-danger btn-sm" onclick="clearAllCache()">
                                    <i class="bi bi-trash-fill"></i> Clear All Cache
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Settings page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Load saved settings
    loadSettings();
    
    // Form submission handlers
    document.getElementById('performanceForm').addEventListener('submit', function(e) {
        e.preventDefault();
        savePerformanceSettings();
    });
    
    document.getElementById('fileProcessingForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveFileSettings();
    });
    
    // OCR confidence slider display
    const ocrSlider = document.getElementById('ocrConfidence');
    const ocrDisplay = document.createElement('span');
    ocrDisplay.className = 'badge bg-primary ms-2';
    ocrSlider.parentNode.appendChild(ocrDisplay);
    
    function updateOcrDisplay() {
        ocrDisplay.textContent = ocrSlider.value;
    }
    
    ocrSlider.addEventListener('input', updateOcrDisplay);
    updateOcrDisplay();
});

function loadSettings() {
    // Load settings from localStorage or server
    const settings = JSON.parse(localStorage.getItem('examGraderSettings') || '{}');
    
    // Apply loaded settings to form elements
    if (settings.llm_model) {
        document.getElementById('llmModel').value = settings.llm_model;
    }
    if (settings.timeout) {
        document.getElementById('processingTimeout').value = settings.timeout;
    }
    // Add more setting loads as needed
}

function savePerformanceSettings() {
    const formData = new FormData(document.getElementById('performanceForm'));
    const settings = Object.fromEntries(formData);
    
    // Save to localStorage (in a real app, this would go to the server)
    const currentSettings = JSON.parse(localStorage.getItem('examGraderSettings') || '{}');
    Object.assign(currentSettings, settings);
    localStorage.setItem('examGraderSettings', JSON.stringify(currentSettings));
    
    // Show success message
    showToast('Performance settings saved successfully!', 'success');
}

function saveFileSettings() {
    const formData = new FormData(document.getElementById('fileProcessingForm'));
    const settings = Object.fromEntries(formData);
    
    // Save to localStorage
    const currentSettings = JSON.parse(localStorage.getItem('examGraderSettings') || '{}');
    Object.assign(currentSettings, settings);
    localStorage.setItem('examGraderSettings', JSON.stringify(currentSettings));
    
    // Show success message
    showToast('File processing settings saved successfully!', 'success');
}

function clearGuideCache() {
    if (confirm('Are you sure you want to clear the guide cache?')) {
        // In a real app, this would make an API call
        showToast('Guide cache cleared successfully!', 'info');
    }
}

function clearSubmissionCache() {
    if (confirm('Are you sure you want to clear the submission cache?')) {
        // In a real app, this would make an API call
        showToast('Submission cache cleared successfully!', 'info');
    }
}

function clearAllCache() {
    if (confirm('Are you sure you want to clear ALL cached data? This cannot be undone.')) {
        // In a real app, this would make an API call
        showToast('All cache cleared successfully!', 'warning');
    }
}

function showToast(message, type = 'info') {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed top-0 end-0 m-3`;
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <i class="bi bi-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close ms-2" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
