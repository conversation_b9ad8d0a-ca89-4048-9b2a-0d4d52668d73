"""
LLM Service for grading exam submissions using DeepSeek Reasoner API.

This module provides a service for integrating with the DeepSeek API to grade
student exam submissions against marking guides.
"""

import os
from typing import Dict, List, Optional, Tuple, Any
import json
import time
import re
import threading

from openai import OpenAI
from dotenv import load_dotenv

from utils.logger import logger

# Load environment variables
load_dotenv()

class LLMServiceError(Exception):
    """Exception raised for errors in the LLM service."""
    pass

class LLMService:
    """
    A service for interacting with the DeepSeek LLM to grade exam submissions.

    This class handles:
    - API key management
    - Prompt construction
    - Response handling
    - Rate limiting and retries

    The primary use case is to compare student answers with model answers
    and provide grading with justification.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: str = "https://api.deepseek.com/v1",
        model: str = "deepseek-chat",  # Use faster model by default
        temperature: float = 0.1,  # Slightly higher for faster responses
        max_retries: int = 2,  # Reduce retries for faster failure
        retry_delay: float = 1.0,  # Reduce retry delay
        seed: Optional[int] = None,  # Disable seed for faster responses
        deterministic: bool = False,  # Disable deterministic mode for speed
        timeout: float = 30.0,  # Add timeout for API calls
        max_tokens: int = 1000  # Limit response length
    ):
        """
        Initialize the LLM service.

        Args:
            api_key: DeepSeek API key (from environment if not provided)
            base_url: DeepSeek API base URL
            model: DeepSeek model name to use
            temperature: Sampling temperature (0.1 for faster responses)
            max_retries: Maximum number of retry attempts for API calls
            retry_delay: Delay between retry attempts in seconds
            seed: Random seed for deterministic outputs (None for faster responses)
            deterministic: Whether to use deterministic mode (False for speed)
            timeout: Timeout for API calls in seconds
            max_tokens: Maximum tokens in response

        Raises:
            LLMServiceError: If API key is not available
        """
        self.api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        if not self.api_key:
            raise LLMServiceError("DeepSeek API key not configured. Set DEEPSEEK_API_KEY in .env")

        self.base_url = base_url
        self.model = model
        self.temperature = temperature
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.seed = seed
        self.deterministic = deterministic
        self.timeout = timeout
        self.max_tokens = max_tokens

        # Log the performance optimizations
        logger.info(f"LLM service initialized with model: {self.model}")
        logger.info(f"Performance settings - Timeout: {self.timeout}s, Max tokens: {self.max_tokens}")
        if self.deterministic:
            logger.info(f"Deterministic mode enabled with seed: {self.seed}")
        else:
            logger.info("Non-deterministic mode enabled for faster responses")

        try:
            # Initialize OpenAI client with timeout support
            client_params = {
                "api_key": self.api_key,
                "base_url": self.base_url,
                "timeout": self.timeout
            }

            # Create the client with timeout support
            self.client = OpenAI(**client_params)
            logger.info(f"LLM client initialized with {self.timeout}s timeout")
        except Exception as e:
            logger.error(f"Failed to initialize LLM service: {str(e)}")
            raise LLMServiceError(f"Failed to initialize LLM service: {str(e)}")

    def test_connection(self) -> bool:
        """
        Test connection to the DeepSeek API.

        Returns:
            bool: True if connection is successful

        Raises:
            LLMServiceError: If connection test fails
        """
        try:
            logger.info("Testing connection to DeepSeek API...")

            # Simple test prompt
            system_prompt = "You are a helpful assistant."
            user_prompt = "Please respond with a simple 'Connection successful' if you receive this message."

            # Make a minimal API call
            params = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": 0.0,
                "max_tokens": 20
            }

            # Add seed parameter if in deterministic mode
            if self.deterministic and self.seed is not None:
                params["seed"] = self.seed

            response = self.client.chat.completions.create(**params)

            # Check if we got a response
            if hasattr(response, 'choices') and len(response.choices) > 0:
                logger.info("Connection test successful")
                return True
            else:
                logger.error("Connection test failed: No valid response received")
                raise LLMServiceError("No valid response received from API")

        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            raise LLMServiceError(f"Failed to connect to DeepSeek API: {str(e)}")

    def compare_answers(
        self,
        question: str,
        guide_answer: str,
        submission_answer: str,
        max_score: int = 10
    ) -> Tuple[float, str]:
        """
        Compare a student's submission answer with the model answer from the marking guide.

        Args:
            question: The question being answered
            guide_answer: The model answer from the marking guide
            submission_answer: The student's submission answer
            max_score: The maximum possible score for this question

        Returns:
            Tuple[float, str]: (Score, Feedback)

        Raises:
            LLMServiceError: If the API call fails
        """
        try:
            # Log the start of answer comparison
            logger.info("Preparing to compare answers...")

            # Construct a prompt for the LLM to compare the answers
            system_prompt = """
            You are an educational grading assistant. Your task is to compare a student's answer
            to a model answer from a marking guide and assign a score based on how well the student's
            answer matches the key points and understanding demonstrated in the model answer.

            Follow these guidelines:
            - Assign a score from 0 to the maximum score
            - Consider how well the student's answer addresses the key points in the model answer
            - Be objective and consistent in your evaluation
            - Focus on content accuracy rather than writing style or formatting
            - Provide a brief explanation for your score

            Your response should be in this JSON format:
            {
                "score": <numeric_score>,
                "feedback": "<brief_explanation>",
                "key_points_matched": ["<point1>", "<point2>", ...],
                "key_points_missed": ["<point1>", "<point2>", ...]
            }
            """

            user_prompt = f"""
            Question: {question}

            Model Answer from Marking Guide: {guide_answer}

            Student's Answer: {submission_answer}

            Maximum Possible Score: {max_score}

            Please evaluate the student's answer and provide a score and feedback.
            """

            # Log sending request
            logger.info("Sending request to LLM service...")

            # Make the API call with optimized settings
            params = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": self.temperature,
                "max_tokens": min(self.max_tokens, 500)  # Use configured max_tokens but cap at 500 for grading
            }

            # Add seed parameter only if in deterministic mode
            if self.deterministic and self.seed is not None:
                params["seed"] = self.seed

            response = self.client.chat.completions.create(**params)

            # Log processing response
            logger.info("Processing LLM response...")

            # Parse the response
            if hasattr(response, 'choices') and len(response.choices) > 0:
                response_text = response.choices[0].message.content.strip()

                # Log extracting results
                logger.info("Extracting score and feedback...")

                try:
                    # Attempt to parse as JSON
                    result = json.loads(response_text)

                    # Extract score and feedback
                    score = float(result.get("score", 0))
                    feedback = result.get("feedback", "No feedback provided")

                    # Ensure score is within bounds
                    score = max(0, min(score, max_score))

                    # Log completion
                    logger.info("Answer comparison completed successfully")

                    return score, feedback

                except json.JSONDecodeError:
                    # If not valid JSON, extract score and feedback manually
                    logger.warning("Response not in valid JSON format. Extracting manually.")

                    # Try to find a numeric score in the response
                    score_match = re.search(r'score[:\s]+(\d+(?:\.\d+)?)', response_text, re.IGNORECASE)
                    score = float(score_match.group(1)) if score_match else 0

                    # Ensure score is within bounds
                    score = max(0, min(score, max_score))

                    # Log completion with manual extraction
                    logger.info("Answer comparison completed with manual extraction")

                    return score, response_text
            else:
                # Log error
                logger.error("No valid response received from API")
                raise LLMServiceError("No valid response received from API")

        except Exception as e:
            logger.error(f"Answer comparison failed: {str(e)}")

            # Return a default score and error message
            return 0, f"Error: {str(e)}"

    def grade_submission(
        self,
        marking_guide_text: str,
        student_submission_text: str,
        max_tokens: int = 2048
    ) -> Dict:
        """
        Grade a student submission against a marking guide.
        This method works with the mapping service to:
        1. Identify questions and answers in both documents
        2. Score each answer based on similarity to the expected answer

        Args:
            marking_guide_text: Full text of the marking guide
            student_submission_text: Full text of the student submission
            max_tokens: Maximum tokens for the response

        Returns:
            Dict: Grading result with scores and feedback
        """
        # Import here to avoid circular imports
        from src.services.mapping_service import MappingService

        # Create mapping service if needed
        mapping_service = MappingService(llm_service=self)

        # Map the submission to the guide
        mapping_result, mapping_error = mapping_service.map_submission_to_guide(
            marking_guide_text,
            student_submission_text
        )

        if mapping_error:
            return {"status": "error", "message": f"Mapping error: {mapping_error}"}

        # Create grading service
        from src.services.grading_service import GradingService
        grading_service = GradingService(llm_service=self, mapping_service=mapping_service)

        # Grade the submission
        grading_result, grading_error = grading_service.grade_submission(
            marking_guide_text,
            student_submission_text
        )

        if grading_error:
            return {"status": "error", "message": f"Grading error: {grading_error}"}

        return grading_result

    def map_submission_to_guide(
        self,
        marking_guide_content: str,
        student_submission_content: str
    ) -> Tuple[Dict, Optional[str]]:
        """
        Map a student submission to a marking guide.
        This is a wrapper around the mapping service functionality.

        Args:
            marking_guide_content: Full text of the marking guide
            student_submission_content: Full text of the student submission

        Returns:
            Tuple[Dict, Optional[str]]: (Mapping result, Error message if any)
        """
        # Import here to avoid circular imports
        from src.services.mapping_service import MappingService

        # Create mapping service
        mapping_service = MappingService(llm_service=self)

        # Map the submission to the guide
        return mapping_service.map_submission_to_guide(
            marking_guide_content,
            student_submission_content
        )